# POST /api/visibility/tripetnshipment

## Description
Retrieves comprehensive trip and shipment details for a specific shift. This endpoint provides detailed information about vehicle tracking, driver details, order data, route information, and real-time trip status.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:9000/api/visibility/tripetnshipment`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body:

```json
{
    "shift_id": 226746,
    "trip_id": "TRIP001"
}
```

### Request Parameters:

| Parameter | Type    | Required | Validation Rules                                           | Description                          |
|-----------|---------|----------|-----------------------------------------------------------|--------------------------------------|
| shift_id  | integer | Yes      | Min: 1, Max: 999999999, Must exist in `shipment` table    | Shipment/Shift ID for trip details   |
| trip_no   | string  | No       | Max: 255 characters                                       | Trip number identifier              |

## Success Response

```json
{
    "status": "success",
    "message": "Trip details retrieved successfully.",
    "data": {
        "last_stop": {
            "createdon": null
        },
        "vehicle": [],
        "record": [],
        "records": [],
        "hos": "N/A",
        "alarm_count": 0,
        "trip_id": 0,
        "travel_dist": 0,
        "isSimBased": "false",
        "drivers": [],
        "driver": [],
        "ref": "",
        "pings": 0,
        "geofencests": "OUT",
        "etime": "N/A",
        "flag": false,
        "routeMapDetails": [],
        "order": {
            "order_id": "",
            "trip_id": 0,
            "user_id": 0,
            "shift_id": 0,
            "plat": 0,
            "plng": 0,
            "dlat": 0,
            "dlng": 0,
            "pickup_endtime": "",
            "drop_endtime": "",
            "id": 0,
            "pickup_address1": "",
            "pickup_city": "",
            "pickup_country": "",
            "pickup_pincode": "",
            "delivery_address1": "",
            "delivery_city": "",
            "delivery_country": "",
            "delivery_pincode": ""
        },
        "timezone": "Asia/Singapore",
        "trip": [],
        "tripicdes": {
            "id": 4,
            "user_id": 1,
            "stime": "2025-10-28 08:00:00",
            "etime": "2026-11-16 08:00:00",
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "scity": "LOC-CHE-1",
            "dcity": "LOC-MUM-1",
            "zone_id": 1,
            "empshift_start": "2025-10-28 08:00:00",
            "empshift_end": "2026-11-16 08:00:00",
            "trip_type": "0",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "shipment_name": "Boxes",
            "shipmentid": "T04425440001",
            "shipment_id": 0,
            "customer_id": 0,
            "transport_mode": "LTL",
            "vendor_id": 2,
            "carrier_type": "0",
            "txnid": "T04425440001",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "domainname": "INFD",
            "schedule_date": null,
            "vehicle_type": null,
            "org_id": 1,
            "be_value": 1,
            "dept_id": null,
            "border_type": null,
            "carrier_instructions": "",
            "status": 1,
            "shift_leg_id": null,
            "origin_id": null,
            "destination_id": null,
            "interchange_control_reference": null,
            "weight_capacity": null,
            "volume_capacity": null,
            "additional_conditions": "",
            "temperature_regime": "",
            "time_for_loading_penality_rate": null,
            "is_carrier_notified": 0,
            "aborted": 0,
            "routetemplate_id": null,
            "template_leg_id": null,
            "sgeolocation": null,
            "egeolocation": null,
            "shipment_order": null,
            "no_of_vehicles": 0,
            "dimension_type": null,
            "truck_start_time": null,
            "max_distance": "0.00",
            "capacity": "1.00",
            "truck_starttime": null,
            "cost_per_hour": "0.00",
            "cost_per_kilometer": "0.00",
            "traffic": null,
            "created_at": null,
            "updated_at": null
        },
        "order_id": "",
        "lguid": 0,
        "id": 0,
        "trip_no": "4",
        "shift_id": 4,
        "stopstatushist": {
            "id": 7,
            "latitude": null,
            "longitude": null,
            "loc_name": null,
            "stop_id": 0,
            "stop_type": "",
            "status_code": "0100",
            "status_id": 9,
            "createdon": "2025-10-28 01:56:32",
            "status_name": "Delivered",
            "startdate": null,
            "enddate": null
        },
        "containerNumber": 0
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:9000/api/visibility/tripetnshipment' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data '{
    "shift_id": 226746,
    "trip_no": "TRIP001"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid user ID or organization ID                 |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response (422):
```json
{
    "status": "error",
    "message": "Validation failed: Shift ID is required.",
    "data": null,
    "errors": [
        "Shift ID is required."
    ]
}
```

### Shift ID Not Exists Error (422):
```json
{
    "status": "error",
    "message": "Validation failed: The selected Shift ID does not exist in the system.",
    "data": null,
    "errors": [
        "The selected Shift ID does not exist in the system."
    ]
}
```

### Authentication Error (401):
```json
{
    "status": "error",
    "message": "Unauthorized access. Please login.",
    "data": null
}
```

### Invalid User ID Error (400):
```json
{
    "status": "error",
    "message": "Invalid user ID.",
    "data": null
}
```

### Server Error (500):
```json
{
    "status": "error",
    "message": "Failed to retrieve trip details.",
    "error": "Error message details"
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Shift ID is required and must exist in the `shipment` table.
- Trip number is optional.
- All timestamps are returned in UTC timezone by default or user's configured timezone.
- Uses Eloquent ORM and database queries for efficient data retrieval.
- Logs errors in storage/logs/ for debugging.
- Validates user authentication, user ID, and organization ID before processing.

---

## Validation Rules

### shift_id
- **Type**: Integer
- **Required**: Yes
- **Minimum Value**: 1
- **Maximum Value**: 999,999,999
- **Database Check**: Must exist in `shipment` table (`id` column)
- **Error Messages**:
  - Missing: "Shift ID is required."
  - Invalid type: "Shift ID must be a valid integer."
  - Out of range: "Shift ID must be at least 1." / "Shift ID cannot exceed 999,999,999."
  - Not exists: "The selected Shift ID does not exist in the system."

### trip_no
- **Type**: String
- **Required**: No (optional)
- **Maximum Length**: 255 characters
- **Error Messages**:
  - Invalid type: "Trip number must be a valid string."
  - Too long: "Trip number cannot exceed 255 characters."

---

## Tests 

### Functional Tests
- `[Pass]` Valid JWT token returns trip details (status 200)
- `[Pass]` Invalid or expired JWT token returns 401
- `[Pass]` Missing shift_id returns 422 validation error
- `[Pass]` Non-existent shift_id returns 422 validation error
- `[Pass]` Invalid shift_id type returns 422 validation error
- `[Pass]` User without valid user ID returns 400

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[Pass]` `Access-Control-Allow-Origin` header present in response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[Pass]` Tested in Postman with valid JWT token
- `[Pass]` Verified response contains expected trip and shipment fields
- `[Pass]` Tested with valid shift_id parameter
- `[Pass]` Tested with missing shift_id parameter
- `[Pass]` Tested with non-existent shift_id parameter
- `[Pass]` Tested with optional trip_no parameter

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified business logic for trip and shipment details retrieval
- Edge cases tested: missing parameters, invalid shift IDs, non-existent shift IDs, unexpected status codes
- Server-side validation implemented with database existence check
