# DELETE /api/visibility/rollbacktripsandshipmentdelete/{id}

## Description
This is a soft delete operation that preserves the shipment data while marking them as deleted. This endpoint is used to rollback trips and shipments.

## Authorization
- Required: Yes (Bearer token from login API)
- Type: JWT Bearer Token

## Request

- Method: DELETE  
- Endpoint: `http://localhost:9000/api/visibility/rollbacktripsandshipmentdelete/{id}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |
| Accept        | application/json                    | Yes      |

### URL Parameters

| Parameter | Type   | Required | Description                    |
|-----------|--------|----------|--------------------------------|
| id        | integer| Yes      | The ID of the shipment to delete |

### Request Body
- No request body required

## Success Response

```json
{
    "status": true,
    "message": "Rollback order trip details and Shipment deleted successfully.",
    "data": {
        "shift_id": "1",
        "deleted_at": "2025-08-20T12:00:17.281349Z"
    }
}
```
### cURL Example:
```sh
curl --location --request DELETE 'http://localhost:9000/api/visibility/rollbacktripsandshipmentdelete/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses

| Code | Message                    | Description                                          |
|------|----------------------------|------------------------------------------------------|
| 400  | Invalid shipment ID         | The provided shipment ID is invalid or not numeric  |
| 401  | Unauthorized              | Invalid, expired, or missing authentication token     |
| 404  | Shipment not found         | Shipment with the specified ID does not exist        |
| 409  | Shipment is already deleted| Shipment status is already 0 (deleted)               |
| 422  | Failed to update shipment status | Database update failed                         |
| 500  | Failed to delete shipment  | Server error during deletion process                 |

### Error Response Examples

**Shipment Not Found (404):**
```json
{
    "status": "error",
    "message": "Shipment not found"
}
```

**Already Deleted (409):**
```json
{
    "status": "error",
    "message": "Shipment is already deleted"
}
```

**Invalid ID (400):**
```json
{
    "status": "error",
    "message": "Invalid shipment ID"
}
```

**Server Error (500):**
```json
{
    "status": "error",
    "message": "Failed to delete shipment: [specific error]"
}
```

## Implementation Details

### Database Changes
- Updates `shipment.status` to `0`
- Sets `shipment.deleted_at` to current timestamp
- Uses direct database queries to ensure reliable updates
- Implements PostgreSQL-specific data type handling

### Soft Delete Behavior
- Shipment data is preserved in the database
- Status is set to 0 (inactive) instead of hard deletion
- `deleted_at` timestamp is set for audit purposes
- Related records remain unchanged as per requirement

### Logging
The API provides comprehensive logging for debugging:
- Shipment details before deletion
- Database update results
- Verification checks after update
- Final status confirmation

## Curl Examples

### Basic Delete Request
```bash
curl -X DELETE "http://localhost:8000/api/visibility/rollbacktripsandshipmentdelete/1" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
```

### With Authentication
```bash
curl -X DELETE "http://localhost:8000/api/visibility/rollbacktripsandshipmentdelete/1" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Database Verification

After successful deletion, verify in the database:

```sql
-- Check if shipment status was updated
SELECT id, name, status, deleted_at FROM shipment WHERE id = 1;
-- Should show: status = 0 and deleted_at = timestamp
```

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- The deletion is a soft delete operation - data is preserved but marked as inactive.
- Related records (contacts, references, accounts, etc.) remain unchanged.
- The API uses direct database queries to ensure reliable updates.
- PostgreSQL-specific data type handling is implemented for the status field.

---

## Tests 

### Functional Tests
- `[Pass]` Valid shipment ID should return `status: success` with final_status = 0
- `[Pass]` Invalid shipment ID should return 400 Bad Request
- `[Pass]` Non-existent shipment ID should return 404 Not Found
- `[Pass]` Already deleted shipment should return 409 Conflict
- `[Pass]` Expired or malformed tokens return 401 Unauthorized

### Database Tests
- `[Pass]` Shipment status should be updated to 0 in database
- `[Pass]` deleted_at timestamp should be set
- `[Pass]` Related records should remain unchanged
- `[Pass]` Final status verification should return 0

### Manual Verification
- `[Pass]` Validate deletion in Postman or Swagger UI
- `[Pass]` Check token validity for secured endpoints
- `[Pass]` Verify database state after deletion
- `[Pass]` Ensure proper error messages for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed soft delete behavior preserves data
- Verified database updates work correctly
- Edge cases tested: invalid IDs, already deleted shipments, server errors
- PostgreSQL-specific data type handling confirmed working

--- 