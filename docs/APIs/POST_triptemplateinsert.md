# POST /api/triptemplates

## Description
Creates a new trip template or updates an existing template for the authenticated user. This API handles both create and update operations based on the presence of `templateRowId` parameter. The template includes constraints for distance, volume, weight, and various service parameters. Sequential ID generation ensures no gaps in the ID sequence.

## Authorization
- **Required**: Yes
- **Type**: JWT Bearer Token

## Request
- **Method**: POST
- **Endpoint**: `http://localhost:9000/api/triptemplates`
- **Headers**:

| Key             | Value                         | Required |
|----------------|-------------------------------|----------|
| Authorization  | Bearer `<JWT Token>`          | Yes      |
| Accept-Language| `en` / `th` / other supported | Optional |
| Content-Type   | application/json               | Yes      |

## Request Body:

```json
{
    "templateName": "Standard FTL Delivery",
    "activeTemplate": 1,
    "templateDescription": "Standard full truckload delivery template",
    "product": "Dry Goods",
    "service": "Normal",
    "orderType": 1,
    "carrierType": "Single Carrier",
    "shipmentType": "Domestic",
    "minimumDistance": 100,
    "minDistanceUom": "KM",
    "maximumDistance": 1000,
    "maxDistanceUom": "KM",
    "minimumVolume": 10,
    "minVolumeUom": "CBM",
    "maximumVolume": 100,
    "maxVolumeUom": "CBM",
    "minimumWeight": 1000,
    "minWeightUom": "KG",
    "maximumWeight": 20000,
    "maxWeightUom": "KG",
    "container_number": "CONT123"
}
```

## Success Response

```json
{
    "status": "true",
    "message": "Trip template saved successfully.",
    "data": {
        "id": 29,
        "template_id": "**********",
        "template_name": "Test Template 21"
    }
}
```

## cURL Examples

```bash
curl -X POST "http://localhost:9000/api/triptemplates" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: en" \
  -d '{
    "templateName": "Standard FTL Delivery",
    "activeTemplate": 1,
    "templateDescription": "Standard full truckload delivery template",
    "product": "Dry Goods",
    "service": "Normal",
    "orderType": 1,
    "carrierType": "Single Carrier",
    "shipmentType": "Domestic",
    "minimumDistance": 100,
    "minDistanceUom": "KM",
    "maximumDistance": 1000,
    "maxDistanceUom": "KM",
    "minimumVolume": 10,
    "minVolumeUom": "CBM",
    "maximumVolume": 100,
    "maxVolumeUom": "CBM",
    "minimumWeight": 1000,
    "minWeightUom": "KG",
    "maximumWeight": 20000,
    "maxWeightUom": "KG",
    "container_number": "CONT123"
}'
```

---

## Error Responses

| Code | Message                          | Description                                     |
|------|----------------------------------|-------------------------------------------------|
| 400  | Invalid organization ID          | User's organization ID is missing or invalid    |
| 401  | Unauthorized access              | Invalid or missing JWT token                    |
| 409  | Trip template already exists     | Template name already exists in organization    |
| 422  | Validation failed                | Missing or invalid required fields              |
| 500  | Failed to save trip template     | Internal server error occurred                  |

---

## Implementation Details

### Authentication
- Uses `Auth::guard('api')->user()` for authentication
- Validates user ID and organization ID from the authenticated user
- Retrieves `org_id` from user's `default_org_id` or `org_id` field

### Business Logic
- **Duplicate Check**: Ensures template names are unique within an organization
- **ID Generation**: Uses week-based sequential ID generation (`TTYYWW####` format)
- **Sequential IDs**: Uses PostgreSQL `setval()` with `is_called=false` to ensure sequential IDs without gaps
- **Template ID Format**: `TT` + Year (2 digits) + Week (2 digits) + Sequence (4 digits)
- **Update vs Create**: Checks `templateRowId` to determine create or update operation
- **Legs Handling**: Saves associated leg data via `saveLegs()` method if provided
- **Status Management**: Sets waypoint status to 0 before saving new legs

### Database Changes
- Inserts into `route_templates` table with sequential ID
- Updates PostgreSQL sequence to prevent ID gaps
- Creates associated records in `route_template_textlegs` if legs are provided
- Updates waypoint status in `waypoint_route_template` table

## Notes

- The API supports both create and update operations based on the `templateRowId` parameter
- Template names must be unique within the organization
- The JWT token must be obtained from the `/api/login` endpoint
- Sequential ID generation ensures no gaps (23, 24, 25, 26...)
- Organization-scoped access ensures users can only manage templates in their organization
- Template ID format: `TT` + 2-digit year + 2-digit week + 4-digit sequence (e.g., `**********`)
- Minimum values can be 0, but defaults are applied if not provided
- UOM fields default to: KM for distance, CBM for volume, KG for weight
- The API uses timezone from config (default: Asia/Kolkata)

## Tests

### Functional Tests
- `[Pass]` Valid JWT token and complete payload returns 200 status with created template data
- `[Pass]` Invalid/missing JWT token returns 401 status code
- `[Pass]` Missing required fields returns 422 validation error
- `[Pass]` Duplicate template name within organization returns 409 conflict error
- `[Pass]` Invalid organization ID returns 400 bad request
- `[Pass]` Update operation with valid templateRowId succeeds
- `[Pass]` Sequential ID generation works without gaps

### Validation Tests
- `[Pass]` Template name with placeholder values (example_, test_, your_) returns validation error
- `[Pass]` Numeric fields with invalid ranges return validation error
- `[Pass]` String fields exceeding max length return validation error
- `[Pass]` activeTemplate with value other than 0 or 1 returns validation error

### Database Tests
- `[Pass]` Sequential IDs are generated correctly (1, 2, 3, 4...)
- `[Pass]` PostgreSQL sequence is properly maintained
- `[Pass]` Duplicate check prevents same template name in organization
- `[Pass]` org_id is correctly assigned from user data

### Manual Verification
- `[Pass]` Tested in Postman with valid JWT token
- `[Pass]` Response structure validated with status, message, and data fields
- `[Pass]` ID sequence verified in database after multiple creates
