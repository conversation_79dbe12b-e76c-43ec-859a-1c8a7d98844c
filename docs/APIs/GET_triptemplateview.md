# GET /api/triptemplates/{id}

## Description
Retrieves details of a specific trip template by its ID for the authenticated user. This endpoint returns the complete template information including all associated legs (route segments) for the template.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:9000/api/triptemplates/{id}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |
| Accept        | application/json                    | Yes      |

### URL Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | integer | Yes | Template ID (1-2147483647) |

## Request Body: None

## Success Response

```json
{
    "status": "true",
    "message": "Trip template retrieved successfully.",
    "data": {
        "id": 1,
        "template_id": "TMP-001",
        "template_name": "Standard FTL Delivery",
        "active": true,
        "description": "Standard Full Truck Load delivery template for domestic shipments",
        "product": "General Freight",
        "service": "FTL",
        "customer_id": 1,
        "order_type": "Standard",
        "carrier_type": "Motor",
        "shipment_type": "Domestic",
        "minimum_distance": "100.00",
        "maximum_distance": "1000.00",
        "minimum_weight": "100.00",
        "maximum_weight": "20000.00",
        "minimum_volume": "10.00",
        "maximum_volume": "100.00",
        "org_id": "1",
        "be_value": "1",
        "legs": []
    }
}
```

### cURL Example:
```sh
# Basic request to get trip template details
curl --location 'http://localhost:9000/api/triptemplates/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--header 'Accept: application/json'
```

---

## Error Responses

| Code | Message | Description |
|------|---------|-------------|
| 400 | Invalid organization ID | Organization ID is missing or invalid |
| 401 | Unauthorized access. Please login. | Invalid, expired, or missing JWT token |
| 404 | Trip template not found. | Template ID not found or access denied |
| 422 | Validation failed | Invalid template ID parameter |
| 500 | Failed to retrieve trip template | Server error during template retrieval |

### Error Response Examples

**Unauthorized (401):**
```json
{
    "status": "false",
    "message": "Unauthorized access.",
    "data": null,
    "errors": []
}
```

**Invalid Organization ID (400):**
```json
{
    "status": "false",
    "message": "Invalid organization ID.",
    "data": null
}
```

**Template Not Found (404):**
```json
{
    "status": "false",
    "message": "Trip template not found.",
    "data": null,
    "errors": []
}
```

**Validation Failed (422):**
```json
{
    "status": "false",
    "message": "Validation failed: Template ID must be at least 1.",
    "data": null,
    "errors": {
        "id": ["Template ID must be at least 1."]
    }
}
```

**Server Error (500):**
```json
{
    "status": "false",
    "message": "Failed to retrieve trip template.",
    "error": "SQL error details..."
}
```

## Implementation Details

### Authentication & Authorization
- Validates JWT token via `Auth::guard('api')->user()`
- Verifies user ID and organization ID
- Only returns templates that belong to the user's organization

### Legs Data
- Includes all active legs (where `status = 1`) associated with the template
- Legs are ordered by `leg_id` for consistent presentation
- Each leg contains route details including origin, destination, carrier, transport mode, and vehicle information
- Empty legs array `[]` is returned if no legs are associated with the template

### Data Columns Returned

**Template Fields:**
- id, template_id, template_name, active, description, product, service, customer_id
- order_type, carrier_type, shipment_type
- minimum_distance, maximum_distance
- minimum_weight, maximum_weight
- minimum_volume, maximum_volume
- org_id, be_value

**Legs Fields (per leg):**
- id, leg_id, origin_id, origin_location, destination_id, destination_location
- carrier_id, transport_mode, vehicle_type, vehicle_id, driver_id, vessel_number

---

## Notes

- The API always returns JSON format (no view rendering)
- Legs are filtered to show only active legs (`status = 1`)
- Original business logic maintained without modification
- Organization access control ensures users can only view templates from their organization
- Route parameter `{id}` must be a valid template ID
- All legs associated with the template are included in the response
- Empty legs array is a valid response if the template has no legs configured

---

## Tests

### Functional Tests
- `[Pass]` Valid JWT token and template ID return 200 status with complete template data
- `[Pass]` Valid JWT token returns legs data in the response
- `[Pass]` Invalid/missing JWT token returns 401 Unauthorized
- `[Pass]` Non-existent template ID returns 404 Not Found
- `[Pass]` Template from different organization returns 404 Not Found
- `[Pass]` Invalid template ID format returns 422 Validation Failed

### CORS & Header Behavior
- `[]` Access-Control-Allow-Origin header present in response
- `[Pass]` Preflight OPTIONS request responded with 204 and correct headers
- `[]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Tested in Postman with valid JWT token from login API
- `[]` Response structure validated with status, message, and data fields
- `[]` Legs data validated with correct structure and fields
- `[]` Edge case: Template with no legs → returns empty legs array
- `[]` Edge case: Invalid template ID → returns appropriate error message
- `[]` Verified all template and legs fields are present in response

### Notes
- Tests performed using Postman with environment variables
- Verified response format: status, message, data
- Edge cases tested: invalid JWT token, non-existent template ID, missing headers, templates with and without legs
- Confirmed business logic preserved from original implementation
- Legs are properly ordered and filtered by status

---
