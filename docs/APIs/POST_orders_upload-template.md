# POST /api/orders/upload-template

## Description
Uploads an orders template file to the server. This endpoint allows users to upload Excel template files (.xlsx, .xls) that will be stored in the server's document storage directory. The uploaded file is validated for type and size, then stored with a unique filename to prevent conflicts.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST
- Endpoint: `http://localhost:9000/api/orders/upload-template`
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | multipart/form-data                 | Yes      |

### Request Body

Form data with file upload:

| Field          | Type   | Required | Description                    |
|----------------|--------|----------|--------------------------------|
| template_file  | file   | Yes      | Excel file (.xlsx, .xls) max 10MB |
| template_file_ | file   | Yes      | Alternative field name (with underscore) |

## Success Response

```json
{
    "status": "true",
    "message": "Orders template uploaded successfully.",
    "data": {
        "file_name": "orders_template_1761288405.xlsx",
        "original_name": "orders_template.xlsx",
        "file_size": 18395,
        "file_path": "assets/poduploads/orders_template_1761288405.xlsx",
        "download_url": "http://localhost:9000/assets/poduploads/orders_template_1761288405.xlsx"
    },
    "errors": []
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:9000/api/orders/upload-template' \
--header 'Authorization: Bearer <your_token_here>' \
--form 'template_file=@"/path/to/your/template.xlsx"'
```

### Alternative cURL Example (with underscore):
```sh
curl --location --request POST 'http://localhost:9000/api/orders/upload-template' \
--header 'Authorization: Bearer <your_token_here>' \
--form 'template_file_=@"/path/to/your/template.xlsx"'
```

## Error Responses

| Code | Message                           | Description                                    |
|------|-----------------------------------|------------------------------------------------|
| 401  | Unauthorized access.              | Invalid, expired, or missing authentication token |
| 400  | User organization ID not set.     | User's organization ID is missing or invalid |
| 422  | Validation failed: [error]        | Missing file, invalid file type, or file too large |
| 422  | Invalid file type. Only Excel files (.xlsx, .xls) are allowed. | File type validation failed |
| 500  | Failed to upload orders template: [error] | Internal server error with exception message |

### Error Response Examples:

**Missing Authentication:**
```json
{
    "status": "false",
    "message": "Unauthorized access.",
    "data": null,
    "errors": []
}
```

**Missing File:**
```json
{
    "status": "false",
    "message": "Validation failed: The template file field is required.",
    "data": null,
    "errors": {
        "template_file": ["The template file field is required."]
    }
}
```

**Invalid File Type:**
```json
{
    "status": "false",
    "message": "Invalid file type. Only Excel files (.xlsx, .xls) are allowed.",
    "data": null,
    "errors": {
        "template_file": "Invalid file type."
    }
}
```

**File Too Large:**
```json
{
    "status": "false",
    "message": "Validation failed: The template file may not be greater than 10240 kilobytes.",
    "data": null,
    "errors": {
        "template_file": ["The template file may not be greater than 10240 kilobytes."]
    }
}
```

---

## Notes

- **Authentication Required**: The endpoint requires a valid JWT Bearer Token for access.
- **Organization Validation**: Ensures the authenticated user has a valid organization ID.
- **File Type Validation**: Only Excel files (.xlsx, .xls) are accepted.
- **File Size Limit**: Maximum file size is 10MB (10240 kilobytes).
- **Unique Filenames**: Files are renamed with timestamp to prevent conflicts.
- **Storage Location**: Files are stored in `public/assets/poduploads/` directory.
- **Directory Security**: Automatic creation of `index.php` to prevent directory browsing.
- **Security Logging**: All upload attempts are logged for monitoring.
- **Error Handling**: Comprehensive error handling with detailed logging for debugging purposes.
- **Consistent Response Format**: All responses follow the same format with `status`, `message`, `data`, and `errors` fields.

## Tests

### Functional Tests
- `[ ]` Tested with valid JWT token and Excel file to verify upload
- `[ ]` Tested with invalid or expired JWT token (status 401)
- `[ ]` Tested with missing authentication token (status 401)
- `[ ]` Tested with invalid organization ID (status 400)
- `[ ]` Tested with missing file (status 422)
- `[ ]` Tested with invalid file type (status 422)
- `[ ]` Tested with file too large (status 422)
- `[ ]` Tested with valid Excel file (.xlsx) (status 200)
- `[ ]` Tested with valid Excel file (.xls) (status 200)
- `[ ]` Tested unique filename generation
- `[ ]` Tested file storage in correct directory
- `[ ]` Tested error logging functionality for debugging

### CORS & Header Behavior
- `[ ]` Confirmed `Access-Control-Allow-Origin` is set correctly
- `[ ]` CORS preflight (OPTIONS) request responds with correct headers
- `[ ]` Multipart form data handling works correctly

### Manual Verification
- `[ ]` Tested using Postman with valid JWT token and Excel file
- `[ ]` Tested with missing JWT token to verify 401 response
- `[ ]` Tested with invalid JWT token to verify 401 response
- `[ ]` Tested with missing file to verify 422 response
- `[ ]` Tested with non-Excel file to verify 422 response
- `[ ]` Tested with oversized file to verify 422 response
- `[ ]` Verified file is stored correctly on server
- `[ ]` Verified unique filename generation
- `[ ]` Confirmed security logging in Laravel logs
- `[ ]` Verified consistent response structure for all cases
- `[ ]` Tested file accessibility via download URL

### Notes
- Tests should include various Excel file sizes and formats.
- Use Postman or curl to validate the endpoint with different file types.
- Verify that uploaded files are accessible via the returned download URL.
- Test with files at the size limit boundary (just under and over 10MB).
- Ensure proper error handling for disk space issues.
- Verify that directory creation and security files are properly set up.
