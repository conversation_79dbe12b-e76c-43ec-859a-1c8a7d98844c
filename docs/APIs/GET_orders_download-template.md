# GET /api/orders/download-template

## Description
Downloads the orders template file information and provides download URL. This endpoint provides a simple way to get the predefined orders template file details without requiring any request parameters. The template file is stored in a fixed location on the server and returns JSON response with file information and download URL.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET
- Endpoint: `http://localhost:9000/api/orders/download-template`
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |

### Request Body
No request body required.

## Success Response

```json
{
    "status": "true",
    "message": "Orders template file is available.",
    "data": {
        "template_path": "assets/poduploads/orders_template.xlsx",
        "file_name": "orders_template.xlsx",
        "download_url": "http://localhost:9000/assets/poduploads/orders_template.xlsx"
    },
    "errors": []
}
```

### cURL Example:
```sh
curl --location --request GET 'http://localhost:9000/api/orders/download-template' \
--header 'Authorization: Bearer <your_token_here>'
```

## Error Responses

| Code | Message                           | Description                                    |
|------|-----------------------------------|------------------------------------------------|
| 401  | Unauthorized access.              | Invalid, expired, or missing authentication token |
| 400  | User organization ID not set.     | User's organization ID is missing or invalid |
| 404  | Orders template file not found.   | Template file does not exist on the server |
| 500  | Failed to download orders template: [error] | Internal server error with exception message |

### Error Response Examples:

**Missing Authentication:**
```json
{
    "status": "false",
    "message": "Unauthorized access.",
    "data": null,
    "errors": []
}
```

**Template File Not Found:**
```json
{
    "status": "false",
    "message": "Orders template file not found.",
    "data": null,
    "errors": {
        "template": "Orders template file not found."
    }
}
```

---

## Notes

- **Authentication Required**: The endpoint requires a valid JWT Bearer token for access.
- **Organization Validation**: Ensures the authenticated user has a valid organization ID.
- **Fixed File Path**: The template file is located at `public/assets/poduploads/orders_template.xlsx`.
- **No Parameters**: No request body or query parameters are required.
- **JSON Response**: Returns success message with file information instead of direct download.
- **File Type**: Template is an Excel (.xlsx) file format.
- **Security Logging**: All download attempts are logged for monitoring.
- **Directory Security**: Automatic creation of `index.php` to prevent directory browsing.
- **Error Handling**: Comprehensive error handling with detailed logging for debugging purposes.
- **Consistent Response Format**: All error responses follow the same format with `status`, `message`, `data`, and `errors` fields.

## Tests

### Functional Tests
- `[ ]` Tested with valid JWT token to verify template information retrieval
- `[ ]` Tested with invalid or expired JWT token (status 401)
- `[ ]` Tested with missing authentication token (status 401)
- `[ ]` Tested with invalid organization ID (status 400)
- `[ ]` Tested with existing template file (status 200)
- `[ ]` Tested with missing template file (status 404)
- `[ ]` Tested JSON response structure
- `[ ]` Tested error logging functionality for debugging
- `[ ]` Verified template file information is returned correctly

### CORS & Header Behavior
- `[ ]` Confirmed `Access-Control-Allow-Origin` is set correctly
- `[ ]` CORS preflight (OPTIONS) request responds with correct headers
- `[ ]` JSON response includes proper Content-Type headers

### Manual Verification
- `[ ]` Tested using Postman with valid JWT token
- `[ ]` Tested with missing JWT token to verify 401 response
- `[ ]` Tested with invalid JWT token to verify 401 response
- `[ ]` Tested with missing template file to verify 404 response
- `[ ]` Verified JSON response contains correct file information
- `[ ]` Confirmed security logging in Laravel logs
- `[ ]` Verified consistent response structure for all error cases
- `[ ]` Tested download URL accessibility

### Notes
- Tests should verify the template file exists at the correct path.
- Use Postman or curl to validate the endpoint.
- Ensure the JSON response contains all required fields.
- Verify proper Content-Type headers for JSON responses.
- Test with different browsers to ensure JSON response works correctly.
