# GET /api/triptemplates

## Description
Fetches a list of trip templates with all required columns for the authenticated user's organization. This endpoint supports general and advanced search functionality with optional filtering parameters.

## Authorization
- Required: Yes
- Type: JWT <PERSON>er Token

## Request

- Method: GET  
- Endpoint: `http://localhost:9000/api/triptemplates`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |
| Accept        | application/json                    | Yes      |

## Request Body: None

## Success Response

```json
{
    "status": "true",
    "message": "Trip templates retrieved successfully.",
    "data": {
        "page": 1,
        "perPage": 15,
        "total": 3,
        "records": [
            {
                "id": 1,
                "template_id": "TMP-001",
                "template_name": "Standard FTL Delivery",
                "active": true,
                "description": "Standard Full Truck Load delivery template for domestic shipments",
                "product": "General Freight",
                "service": "FTL",
                "customer_id": 1,
                "order_type": "Standard",
                "carrier_type": "Motor",
                "shipment_type": "Domestic",
                "minimum_distance": "100.00",
                "maximum_distance": "1000.00",
                "minimum_weight": "100.00",
                "maximum_weight": "20000.00",
                "minimum_volume": "10.00",
                "maximum_volume": "100.00",
                "org_id": "1",
                "be_value": "1"
            },
            {
                "id": 2,
                "template_id": "TMP-002",
                "template_name": "Express LTL Service",
                "active": true,
                "description": "Less Than Truck Load express service for urgent deliveries",
                "product": "Express Freight",
                "service": "percentage",
                "customer_id": 1,
                "order_type": "Express",
                "carrier_type": "Motor",
                "shipment_type": "Domestic",
                "minimum_distance": "50.00",
                "maximum_distance": "500.00",
                "minimum_weight": "10.00",
                "maximum_weight": "5000.00",
                "minimum_volume": "1.00",
                "maximum_volume": "30.00",
                "org_id": "1",
                "be_value": "1"
            },
            {
                "id": 3,
                "template_id": "TMP-003",
                "template_name": "International Cross-Border",
                "active": true,
                "description": "Cross-border international shipment template with customs handling",
                "product": "International Freight",
                "service": "Cross Border",
                "customer_id": 2,
                "order_type": "International",
                "carrier_type": "Air",
                "shipment_type": "International",
                "minimum_distance": "500.00",
                "maximum_distance": "5000.00",
                "minimum_weight": "50.00",
                "maximum_weight": "10000.00",
                "minimum_volume": "5.00",
                "maximum_volume": "50.00",
                "org_id": "1",
                "be_value": "1"
            }
        ]
    }
}
```

### cURL Example:
```sh
# Basic request (returns first 15 records)
curl --location 'http://localhost:9000/api/triptemplates' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'

# With pagination parameters
curl --location 'http://localhost:9000/api/triptemplates?per_page=10&page=2' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses

| Code | Message | Description |
|------|---------|-------------|
| 400 | Invalid user ID | User ID is missing or invalid |
| 400 | Invalid organization ID | Organization ID is missing or invalid |
| 401 | Unauthorized access. Please login. | Invalid, expired, or missing JWT token |
| 403 | Unauthorized access to this branch | User cannot access requested be_value |
| 422 | Validation failed | Request parameter validation error |
| 500 | Failed to retrieve trip templates | Server error during template retrieval |

### Error Response Examples

**Unauthorized (401):**
```json
{
    "status": "error",
    "message": "Unauthorized access. Please login.",
    "data": null
}
```

**Invalid Organization ID (400):**
```json
{
    "status": "error",
    "message": "Invalid organization ID.",
    "data": null
}
```

**Validation Failed (422):**
```json
{
    "status": "error",
    "message": "Validation failed: Service ID must be at least 1.",
    "data": null,
    "errors": [
        "Service ID must be at least 1."
    ]
}
```

**Server Error (500):**
```json
{
    "status": "false",
    "message": "Failed to retrieve trip templates.",
    "error": "SQL error details..."
}
```

## Implementation Details

### Authentication & Authorization
- Validates JWT token via `Auth::guard('api')->user()`
- Verifies user ID and organization ID
- Uses user's default organization ID if `org_id` not provided

### Search Functionality
- **General Search**: Filters by template_id and/or template_name
- **Advanced Search**: Additional filters for active status, service, and customer_id
- Filters are applied based on org_id and be_value when applicable

### Database Query
- Explicitly selects all 18 required columns from route_templates table
- Returns empty records array if no templates found (status: true)
- Customer lookup uses 'name' column instead of 'code'

### Pagination
- Uses Laravel's built-in `paginate()` method
- Default: 15 records per page
- Supports `per_page` parameter (minimum: 1)
- Supports `page` parameter (minimum: 1)
- Returns `page`, `perPage`, `total`, and `records` in response

### Data Columns Returned
All required columns are included in the response: template_id, template_name, active, description, product, service, customer_id, order_type, carrier_type, shipment_type, min_distance, max_distance, min_weight, max_weight, min_volume, max_volume, org_id, be_value

---

## Notes

- The API always returns JSON format (no view rendering)
- Empty results return status "true" with empty records array in pagination structure
- Original business logic maintained without modification
- Seeder available for test data population (`RouteTemplatesSeeder`)
- Debug logging enabled for query troubleshooting
- Pagination follows same pattern as OrderTypeController and OrderController
- All query parameters are optional for backward compatibility

---

## Tests

### Functional Tests
- `[Pass]` Valid JWT token returns 200 status with paginated data structure
- `[Pass]` Invalid/missing JWT token returns 401 Unauthorized
- `[Pass]` No trip templates available returns status "true" with empty records array
- `[Pass]` General search filters templates correctly
- `[Pass]` Advanced search filters templates correctly
- `[Pass]` Pagination returns correct page, perPage, total, and records
- `[]` Pagination with custom per_page parameter works correctly

### CORS & Header Behavior
- `[]` Access-Control-Allow-Origin header present in response
- `[Pass]` Preflight OPTIONS request responded with 204 and correct headers
- `[]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Tested in Postman with valid JWT token from login API
- `[]` Response structure validated with status, message, and paginated data structure
- `[]` Edge case: No trip templates → returns status "true" with empty records array
- `[]` Verified all 16 required columns are present in records array
- `[]` Verified pagination metadata (page, perPage, total) is correct

### Notes
- Tests performed using Postman with environment variables
- Verified response format: status, message, data
- Edge cases tested: invalid JWT token, missing headers, empty results
- Confirmed business logic preserved from original implementation

---
