# GET /api/visibility/shipments

## Description
Fetches the Shipments for the authenticated user.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:9000/api/visibility/shipments`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body: None

## Success Response

```json
{
    "success": true,
    "message": "shipments retrieved successfully.",
    "data": {
        "1": {
            "id": 1,
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "customer_name": null,
            "carrier_name": null,
            "driver_name": null,
            "vehicle_number": null,
            "stops": [
                {
                    "id": 1,
                    "shipment_id": 1,
                    "plat": "13.0827",
                    "plng": "80.2707",
                    "stoptype": "P",
                    "stopstatus": 1,
                    "employee_id": 1
                },
                {
                    "id": 2,
                    "shipment_id": 1,
                    "plat": "18.5204",
                    "plng": "73.8567",
                    "stoptype": "D",
                    "stopstatus": 0,
                    "employee_id": 1
                }
            ]
        },
        "2": {
            "id": 2,
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "customer_name": null,
            "carrier_name": null,
            "driver_name": null,
            "vehicle_number": null,
            "stops": [
                {
                    "id": 3,
                    "shipment_id": 2,
                    "plat": "13.0827",
                    "plng": "80.2707",
                    "stoptype": "P",
                    "stopstatus": 0
                },
                {
                    "id": 4,
                    "shipment_id": 2,
                    "plat": "18.5204",
                    "plng": "73.8567",
                    "stoptype": "D",
                    "stopstatus": 0
                }
            ]
        },
        "3": {
            "id": 3,
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "customer_name": null,
            "carrier_name": null,
            "driver_name": null,
            "vehicle_number": null,
            "stops": [
                {
                    "id": 5,
                    "shipment_id": 3,
                    "plat": "13.0827",
                    "plng": "80.2707",
                    "stoptype": "P",
                    "stopstatus": 0
                },
                {
                    "id": 6,
                    "shipment_id": 3,
                    "plat": "18.5204",
                    "plng": "73.8567",
                    "stoptype": "D",
                    "stopstatus": 0
                }
            ]
        },
        "4": {
            "id": 4,
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "customer_name": null,
            "carrier_name": null,
            "driver_name": null,
            "vehicle_number": null,
            "stops": [
                {
                    "id": 7,
                    "shipment_id": 4,
                    "plat": "13.0827",
                    "plng": "80.2707",
                    "stoptype": "P",
                    "stopstatus": 1,
                    "employee_id": 4
                },
                {
                    "id": 8,
                    "shipment_id": 4,
                    "plat": "18.5204",
                    "plng": "73.8567",
                    "stoptype": "D",
                    "stopstatus": 0,
                    "employee_id": 4
                }
            ]
        },
        "5": {
            "id": 5,
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "customer_name": null,
            "carrier_name": null,
            "driver_name": null,
            "vehicle_number": null,
            "stops": [
                {
                    "id": 9,
                    "shipment_id": 5,
                    "plat": "13.0827",
                    "plng": "80.2707",
                    "stoptype": "P",
                    "stopstatus": 1,
                    "employee_id": 5
                },
                {
                    "id": 10,
                    "shipment_id": 5,
                    "plat": "18.5204",
                    "plng": "73.8567",
                    "stoptype": "D",
                    "stopstatus": 0,
                    "employee_id": 5
                }
            ]
        },
        "6": {
            "id": 6,
            "splace": "LOC-CHE-1",
            "slat": "13.08270000",
            "slng": "80.27070000",
            "eplace": "LOC-MUM-1",
            "elat": "18.52040000",
            "elng": "73.85670000",
            "weight": "0.00",
            "volume": "0.00",
            "units": "0.00",
            "startdate": "2025-10-28",
            "enddate": "2026-11-16",
            "customer_name": null,
            "carrier_name": null,
            "driver_name": null,
            "vehicle_number": null,
            "stops": [
                {
                    "id": 11,
                    "shipment_id": 6,
                    "plat": "13.0827",
                    "plng": "80.2707",
                    "stoptype": "P",
                    "stopstatus": 1,
                    "employee_id": 6
                },
                {
                    "id": 12,
                    "shipment_id": 6,
                    "plat": "18.5204",
                    "plng": "73.8567",
                    "stoptype": "D",
                    "stopstatus": 0,
                    "employee_id": 6
                }
            ]
        }
    },
    "count": 6,
    "pagination": {
        "total": 12,
        "per_page": 4,
        "current_page": 1,
        "last_page": 3
    }
}
```

### cURL Example:
```sh
curl --location --request GET 'http://localhost:9000/api/visibility/shipments' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- Ensure proper token handling for password reset and session-sensitive flows.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing head

---
