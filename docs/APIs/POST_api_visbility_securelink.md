# POST /api/visibility/securelink

## Description
Fetches the secure link details for the authenticated user.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:9000/api/visibility/securelink`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body: 
```json
{
    "ship_id": "TKN3125310002",
    "vendor_id": 1312,
    "shipmentid": "TKN3125310002",
    "voyage_number": "TKN3125310002"
}
```

## Success Response

```json
{
    "status": "success",
    "message": "Secure link generated successfully.",
    "data": {
        "link_id": "http://localhost:8000driverappv3/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJUS04zMTI1MzEwMDAyIiwiZXhwIjoxNzYyNDk3NzYxLCJpc3MiOiJrbjEzMTIiLCJpYXQiOjE3NjE2MzM3NjF9.QAHlhe0vYPZvIt1F2xJOqDkbgAOJ36id2crOg1I",
        "voyage_number": "TKN3125310003",
        "carrier_link_id": "http://localhost:8000knmcdbkg/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJrbjEzMTIiLCJzdWIiOiJUS04zMTI1MzEwMDAyIiwiaWF0IjoxNzYxNjMzNzYxLCJleHAiOjE3NjE3MjAxNjF9.JaSUXaeXEGMmSPfBfOzTRSlfPWph6UjsPIbiIk91le0",
        "errors": []
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:9000/api/visibility/securelink' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data '{
    "ship_id": "TKN3125310002",
    "vendor_id": 1312,
    "shipmentid": "TKN3125310002",
    "voyage_number": "TKN3125310002"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- Ensure proper token handling for password reset and session-sensitive flows.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing head

---
