# POST /api/visibility/assignvehicle

## Description
Fetches the assign vehicle details for the authenticated user.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:9000/api/visibility/assignvehicle`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body: 
```json
{
    "shift_id": "227523",
    "carrier_id": "1312",
    "volume": null,
    "vehicle_id": "16224"
}
```

## Success Response

```json
{
    "status": "success",
    "message": "Shipment details retrieved for Assign Vehicle successfully.",
    "data": {
        "driver": 0,
        "volume": null,
        "vehicle_id": "16224",
        "shipment_id": "227523",
        "carrier_id": "1312",
        "schedule_date": "2025-07-24 23:15:38",
        "carrier_instructions": null,
        "weight_capacity": null,
        "volume_capacity": null,
        "additional_conditions": null,
        "temperature_regime": null,
        "time_for_loading_penality_rate": null,
        "vehicle_type": null,
        "category": null,
        "rate": null,
        "carrier_code": null,
        "rate_categories": []
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:9000/api/visibility/assignvehicle' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data '{
    "shift_id": "227523",
    "carrier_id": "1312",
    "volume": null,
    "vehicle_id": "16224"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- Ensure proper token handling for password reset and session-sensitive flows.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing head

---
