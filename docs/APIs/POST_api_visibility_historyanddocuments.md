# POST /api/visibility/historyanddocuments

## Description
Retrieves detailed information about a specific shift including history and ePOD data. This endpoint provides comprehensive shift details for viewing and editing purposes.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:9000/api/visibility/historyanddocuments`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body:

```json
{
    "shift_id": "227507",
    "stop_id": null,
    "pickup_id": null,
    "order_id": null
}
```

## Success Response

```json
{
    "status": "success",
    "message": "History and documents retrieved successfully.",
    "data": {
        "drivers": [],
        "primary_driver": "",
        "pods": [],
        "history": [],
        "slat": "",
        "slng": "",
        "dlat": "",
        "dlng": ""
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:9000/api/visibility/historyanddocuments' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data '{
    "shift_id": "227507",
    "stop_id": null,
    "pickup_id": null,
    "order_id": null
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 403  | Forbidden         | User doesn't have required permissions             |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Error Response Example:
```json
{
    "success": false,
    "message": "Error retrieving shift details",
    "error": "Database connection error"
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Shift ID is required in the request body.
- All timestamps are returned in ISO 8601 format.
- Uses Eloquent ORM for efficient querying and relationship handling.
- Logs errors in writable/logs/ for debugging.

---

## Tests 

### Functional Tests
- `[Pass]` Valid JWT token returns shift details (status 200)
- `[Pass]` Invalid or expired JWT token returns 401
- `[Pass]` User without permissions returns 403
- `[Pass]` Missing shift_id returns 400 Bad Request
- `[Pass]` Invalid shift_id returns 404 Not Found
- `[Pass]` Valid shift_id returns complete shift details
- `[Pass]` History is correctly retrieved
- `[Pass]` ePOD data is correctly retrieved

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[Pass]` `Access-Control-Allow-Origin` header present in response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[Pass]` Tested in Postman with valid JWT token
- `[Pass]` Verified response contains expected shift fields
- `[Pass]` Tested with valid shift_id parameter
- `[Pass]` Tested with missing shift_id parameter
- `[Pass]` Verified history retrieval
- `[Pass]` Verified ePOD data retrieval

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified business logic for shift details retrieval
- Edge cases tested: missing parameters, invalid shift IDs, unexpected status codes 