# POST /api/visibility/shownearbyvehicles

## Description
Retrieves comprehensive nearby vehicles for a specific shift, carrier, and location. This endpoint provides comprehensive nearby vehicles details for viewing and editing purposes.

## Authorization
- Required: Yes
- Type: JWT <PERSON>er Token

## Request

- Method: POST  
- Endpoint: `http://localhost:9000/api/visibility/shownearbyvehicles`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body:

```json
{
    "shift_id": "226746",
    "carrier_id": "1312",
    "latitude": 28.5692,
    "longitude": 77.2408
}
```

## Success Response

```json
{
    "status": "success",
    "message": "Nearby vehicles retrieved successfully.",
    "data": {
        "shift_id": "226746",
        "carrier_id": "1312",
        "pickup_latitude": 28.5692,
        "pickup_longitude": 77.2408,
        "vehicles": [
            {
                "id": 5,
                "lat": 23.0225,
                "lng": 72.5714,
                "vehicleNumber": "TRK005",
                "driverName": "Vikram <PERSON>",
                "driverEmail": null,
                "driverPhone": "+91-9876543214",
                "vehicleId": 5
            },
            {
                "id": 4,
                "lat": 13.0827,
                "lng": 80.2707,
                "vehicleNumber": "TRK004",
                "driverName": "Mohan Reddy",
                "driverEmail": null,
                "driverPhone": "+91-9876543213",
                "vehicleId": 4
            },
            {
                "id": 3,
                "lat": 28.7041,
                "lng": 77.1025,
                "vehicleNumber": "TRK003",
                "driverName": "Suresh Patel",
                "driverEmail": null,
                "driverPhone": "+91-9876543212",
                "vehicleId": 3
            },
            {
                "id": 2,
                "lat": 19.076,
                "lng": 72.8777,
                "vehicleNumber": "TRK002",
                "driverName": "Amit Singh",
                "driverEmail": null,
                "driverPhone": "+91-9876543211",
                "vehicleId": 2
            },
            {
                "id": 1,
                "lat": 12.9716,
                "lng": 77.5946,
                "vehicleNumber": "TRK001",
                "driverName": "Rajesh Kumar",
                "driverEmail": null,
                "driverPhone": "+91-9876543210",
                "vehicleId": 1
            }
        ]
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:9000/api/visibility/shownearbyvehicles' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data '{
    "shift_id": "226746",
    "carrier_id": "1312",
    "latitude": 28.5692,
    "longitude": 77.2408
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 403  | Forbidden         | User doesn't have required permissions             |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Error Response Example:
```json
{
    "status": "error",
    "message": "Error retrieving nearby vehicles."
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Shift ID is required in the request body.
- All timestamps are returned in ISO 8601 format.
- Uses Eloquent ORM for efficient querying and relationship handling.
- Logs errors in writable/logs/ for debugging.

---

## Tests 

### Functional Tests
- `[Pass]` Valid JWT token returns shift details (status 200)
- `[Pass]` Invalid or expired JWT token returns 401
- `[Pass]` User without permissions returns 403
- `[Pass]` Missing shift_id returns 400 Bad Request
- `[Pass]` Valid shift_id returns complete TWB details

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[Pass]` `Access-Control-Allow-Origin` header present in response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[Pass]` Tested in Postman with valid JWT token
- `[Pass]` Verified response contains expected shift fields
- `[Pass]` Tested with valid shift_id parameter
- `[Pass]` Tested with missing shift_id parameter

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified business logic for nearby vehicles retrieval
- Edge cases tested: missing parameters, invalid IDs, unexpected status codes 