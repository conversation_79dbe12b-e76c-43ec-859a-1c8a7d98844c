# DELETE /api/triptemplates/{id}

## Description
Deletes a specific trip template by its ID using soft delete (sets `active` to 0). This operation is restricted to templates within the authenticated user's organization. The template must exist and belong to the user's organization for deletion to succeed.

## Authorization
- **Required**: Yes
- **Type**: JWT Bearer Token

## Request
- **Method**: DELETE
- **Endpoint**: `http://localhost:9000/api/triptemplates/{id}`
- **Headers**:

| Key             | Value                         | Required |
|----------------|-------------------------------|----------|
| Authorization  | Bearer `<JWT Token>`          | Yes      |
| Accept-Language| `en` / `th` / other supported | Optional |
| Content-Type   | application/json               | Yes      |

## Request Body
- None

## Success Response - Status Code: 200

```json
{
    "status": "true",
    "message": "Trip template deleted successfully.",
    "data": {
        "deleted_template_id": 29,
        "deletion_method": "soft_delete"
    }
}
```

## Error Responses

| Code | Message                          | Description                                     |
|------|----------------------------------|-------------------------------------------------|
| 400  | Invalid organization ID          | User's organization ID is missing or invalid    |
| 401  | Unauthorized access              | Invalid or missing JWT token                    |
| 404  | Template not found or access denied | Template does not exist or belongs to different organization |
| 422  | Validation failed                | Invalid template ID parameter                   |
| 500  | Failed to delete trip template   | Internal server error occurred                  |

---

## cURL Example

```bash
curl -X DELETE "http://localhost:9000/api/triptemplates/29" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: en"
```

## Implementation Details

### Authentication
- Uses `Auth::guard('api')->user()` for authentication
- Validates user ID and organization ID from the authenticated user
- Retrieves `org_id` from user's `default_org_id` or `org_id` field

### Business Logic
- **Soft Delete**: Sets `active = 0` instead of hard deleting the record
- **Organization Scoped**: Only allows deletion of templates within the user's organization
- **Existence Check**: Verifies template exists before attempting deletion
- **Access Control**: Ensures user can only delete templates they own

### Database Changes
- Updates `route_templates` table setting `active = 0` for the specified template
- Filters by `org_id` to ensure organization-scoped deletion
- No cascade deletes - associated data remains intact

### Response Status Values
- `"true"`: Successful deletion
- `"false"`: Failed deletion

## Notes

- The JWT token must be obtained from the `/api/login` endpoint
- Templates are soft deleted (active status set to 0), not permanently removed
- Users can only delete templates belonging to their organization
- The template ID must exist in the database for the operation to succeed
- Deletion is logged for audit purposes
- The API returns detailed error messages for debugging

## Tests

### Functional Tests
- `[Pass]` Valid JWT token and existing template ID returns 200 with success message
- `[Pass]` Invalid/missing JWT token returns 401 status code
- `[Pass]` Non-existent template ID returns 404 status code
- `[Pass]` Template from different organization returns 404 status code
- `[Pass]` Invalid organization ID returns 400 status code

### Validation Tests
- `[Pass]` Invalid template ID (non-integer) returns 422 validation error
- `[Pass]` Template ID less than 1 returns 422 validation error
- `[Pass]` Non-existent template ID returns 422 validation error (exists rule)

### Security Tests
- `[Pass]` Organization-scoped access prevents cross-organization deletion
- `[Pass]` Authentication required for all requests
- `[Pass]` User organization validation works correctly

### Manual Verification
- `[Pass]` Tested in Postman with valid JWT token
- `[Pass]` Response structure validated with status, message, and data fields
- `[Pass]` Database verified to show active = 0 after successful deletion
- `[Pass]` Template remains in database but marked as inactive
