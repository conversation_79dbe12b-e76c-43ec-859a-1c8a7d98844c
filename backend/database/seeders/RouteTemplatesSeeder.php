<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RouteTemplatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $routeTemplates = [
            [
                'id' => 1,
                'template_id' => 'TMP-001',
                'template_name' => 'Standard FTL Delivery',
                'description' => 'Standard Full Truck Load delivery template for domestic shipments',
                'product' => 'General Freight',
                'service' => 'FTL',
                'customer_id' => 1,
                'order_type' => 'Standard',
                'carrier_type' => 'Motor',
                'shipment_type' => 'Domestic',
                'min_distance' => 100.00,
                'mindistance_units' => 'KM',
                'max_distance' => 1000.00,
                'maxdistance_units' => 'KM',
                'min_weight' => 100.00,
                'minweight_units' => 'KG',
                'max_weight' => 20000.00,
                'maxweight_units' => 'KG',
                'min_volume' => 10.00,
                'minvolume_units' => 'CBM',
                'max_volume' => 100.00,
                'maxvolume_units' => 'CBM',
                'container_number' => null,
                'active' => 1,
                'user_id' => 1,
                'status' => 1,
                'org_id' => '1',
                'be_value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'template_id' => 'TMP-002',
                'template_name' => 'Express LTL Service',
                'description' => 'Less Than Truck Load express service for urgent deliveries',
                'product' => 'Express Freight',
                'service' => 'percentage',
                'customer_id' => 1,
                'order_type' => 'Express',
                'carrier_type' => 'Motor',
                'shipment_type' => 'Domestic',
                'min_distance' => 50.00,
                'mindistance_units' => 'KM',
                'max_distance' => 500.00,
                'maxdistance_units' => 'KM',
                'min_weight' => 10.00,
                'minweight_units' => 'KG',
                'max_weight' => 5000.00,
                'maxweight_units' => 'KG',
                'min_volume' => 1.00,
                'minvolume_units' => 'CBM',
                'max_volume' => 30.00,
                'maxvolume_units' => 'CBM',
                'container_number' => null,
                'active' => 1,
                'user_id' => 1,
                'status' => 1,
                'org_id' => '1',
                'be_value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 3,
                'template_id' => 'TMP-003',
                'template_name' => 'International Cross-Border',
                'description' => 'Cross-border international shipment template with customs handling',
                'product' => 'International Freight',
                'service' => 'Cross Border',
                'customer_id' => 2,
                'order_type' => 'International',
                'carrier_type' => 'Air',
                'shipment_type' => 'International',
                'min_distance' => 500.00,
                'mindistance_units' => 'KM',
                'max_distance' => 5000.00,
                'maxdistance_units' => 'KM',
                'min_weight' => 50.00,
                'minweight_units' => 'KG',
                'max_weight' => 10000.00,
                'maxweight_units' => 'KG',
                'min_volume' => 5.00,
                'minvolume_units' => 'CBM',
                'max_volume' => 50.00,
                'maxvolume_units' => 'CBM',
                'container_number' => null,
                'active' => 1,
                'user_id' => 1,
                'status' => 1,
                'org_id' => '1',
                'be_value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 4,
                'template_id' => 'TMP-004',
                'template_name' => 'Premium Door-to-Door',
                'description' => 'Premium door-to-door delivery service with handling and installation',
                'product' => 'Premium Service',
                'service' => 'Door to Door',
                'customer_id' => 1,
                'order_type' => 'Premium',
                'carrier_type' => 'Motor',
                'shipment_type' => 'Door to Door',
                'min_distance' => 0.00,
                'mindistance_units' => 'KM',
                'max_distance' => 2000.00,
                'maxdistance_units' => 'KM',
                'min_weight' => 0.00,
                'minweight_units' => 'KG',
                'max_weight' => 10000.00,
                'maxweight_units' => 'KG',
                'min_volume' => 0.00,
                'minvolume_units' => 'CBM',
                'max_volume' => 50.00,
                'maxvolume_units' => 'CBM',
                'container_number' => null,
                'active' => 1,
                'user_id' => 1,
                'status' => 1,
                'org_id' => '44',
                'be_value' => '0',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // Use upsert to avoid duplicate key errors
        foreach ($routeTemplates as $template) {
            DB::table('route_templates')->updateOrInsert(
                ['id' => $template['id']],
                $template
            );
        }
    }
}

