<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SxUserPrevilegeAccessRule extends Model
{
    use SoftDeletes;

    protected $table = 'sx_user_previlege_access_rules';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $dates = ['deleted_at'];

    protected $fillable = [
        'user_id',
        'previllege_id',
        'previllege_module_id',
        'feature_id',
        'created_by',
        'updated_by',
        'org_id',
        'view_access',
        'add_access',
        'modify_access',
        'delete_access',
        'status',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'previllege_id' => 'integer',
        'previllege_module_id' => 'integer',
        'feature_id' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'org_id' => 'integer',
        'view_access' => 'integer',
        'add_access' => 'integer',
        'modify_access' => 'integer',
        'delete_access' => 'integer',
        'status' => 'integer',
    ];

    /**
     * Relationship to SxUsers
     */
    public function user()
    {
        return $this->belongsTo(SxUsers::class, 'user_id', 'id');
    }

    /**
     * Relationship to SxPrevilleges
     */
    public function privilege()
    {
        return $this->belongsTo(SxPrevilleges::class, 'previllege_id', 'id');
    }

    /**
     * Relationship to PrivilegeModule
     */
    public function privilegeModule()
    {
        return $this->belongsTo(PrivilegeModule::class, 'previllege_module_id', 'id');
    }

    /**
     * Relationship to ModuleFeature
     */
    public function moduleFeature()
    {
        return $this->belongsTo(ModuleFeature::class, 'feature_id', 'id');
    }

    /**
     * Relationship to SxOrganization
     */
    public function organization()
    {
        return $this->belongsTo(SxOrganization::class, 'org_id', 'id');
    }

    /**
     * Scope for active records
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope for inactive records
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * Get active records
     */
    public static function getActiveRecords()
    {
        return self::active()->orderBy('id', 'desc')->get()->toArray();
    }

    /**
     * Get inactive records
     */
    public static function getInActiveRecords()
    {
        return self::inactive()->orderBy('id', 'desc')->get()->toArray();
    }

    /**
     * Get deleted records
     */
    public static function getDeletedRecords()
    {
        return self::onlyTrashed()->orderBy('id', 'desc')->get()->toArray();
    }
}
