<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShiftVehicle extends Model
{
    protected $table = 'shft_veh';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'user_id',
        'route_id',
        'shft_id',
        'carrier_id',
        'vehicle_id',
        'trailer_id',
        'register_number',
        'status',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'route_id' => 'integer',
        'shft_id' => 'integer',
        'carrier_id' => 'integer',
        'vehicle_id' => 'integer',
        'trailer_id' => 'integer',
        'status' => 'integer',
        'created_on' => 'datetime',
        'updated_on' => 'datetime',
    ];

    // Define relationships
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function shift()
    {
        return $this->belongsTo(Shipment::class, 'shft_id');
    }

    public function carrier()
    {
        return $this->belongsTo(User::class, 'carrier_id');
    }

    public function vehicle()
    {
        return $this->belongsTo(TrucksData::class, 'vehicle_id');
    }

}