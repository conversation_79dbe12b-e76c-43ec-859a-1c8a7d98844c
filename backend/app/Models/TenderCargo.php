<?php

namespace App\Models;
use App\Models\CargoDetail;

use Illuminate\Database\Eloquent\Model;

class TenderCargo extends Model
{
    protected $table = 'tender_cargos';

    public $incrementing = true;
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'tender_id',
        'cargo_id',
        'status_old',
        'status',
    ];

    protected $casts = [
        'tender_id' => 'integer',
        'cargo_id' => 'integer',
        'status_old' => 'integer',
        'status' => 'integer',
    ];

    public function cargoDetail()
    {
        return $this->belongsTo(CargoDetail::class, 'cargo_id');
    }

}