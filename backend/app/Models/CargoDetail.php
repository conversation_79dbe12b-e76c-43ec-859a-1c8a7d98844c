<?php

namespace App\Models;
use App\Models\TenderCargo;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CargoDetail extends Model
{
    protected $table = 'cargo_details';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'cargo_type',
        'cargotype_id',
        'handling_unit',
        'length',
        'length_unit',
        'width',
        'width_unit',
        'height',
        'height_unit',
        'weight',
        'weight_unit',
        'volume',
        'volume_unit',
        'createdby',
        'updatedby',
        'status',
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    public function cargoType(): BelongsTo
    {
        return $this->belongsTo(CargoType::class, 'cargotype_id');
    }

    public function tenderCargos()
    {
        return $this->hasMany(TenderCargo::class, 'cargo_id');
    }
}