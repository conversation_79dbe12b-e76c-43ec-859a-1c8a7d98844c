<?php

namespace App\Http\Controllers;

use App\Models\CountryMaster;
use App\Models\InnerCargo;
use App\Models\OrderType;
use App\Models\SxPartyMembers;
use App\Models\ResolutionMaster;
use App\Models\StoppageMaster;
use App\Models\VasMaster;
use App\Models\SxPartyTypes;
use App\Models\ChargeCode;
use App\Models\VatCategory;
use App\Models\CostCenter;
use App\Models\TransportMode;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\User;
use App\Models\ShipunitType;
use App\Models\OrderCargodetail;
use App\Models\CargoDetail;
use App\Models\OrderParty;
use App\Models\OrderReference;
use App\Models\Item;
use App\Models\Revenue;
use App\Models\ReferenceMaster;
use App\Models\OrderpartyAddress;
use App\Models\CarrierAllocationRule;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\TripOrderToShipmentRequest;
use App\Http\Requests\CopyOrderRequest;
use App\Http\Requests\ReverseOrderRequest;
use Illuminate\Support\Facades\Validator;

use App\Http\Requests\StoreOrderRequest;

use App\Services\OrderList\AdvancedSearch;
use App\Services\OrderList\ExcelUpload;
use App\Services\OrderList\Properties;
use App\Services\OrderList\Search;
use App\Services\OrderList\GetArgumentsResolver;
use App\Services\OrderList\OrderProcessor;
use App\Services\OrderList\OrderQueryService;
use App\Services\TripCreateFromOrders;
use App\Services\RateManagement;
use App\Services\OrderStore;
use App\Services\PartyManagementService;

use Carbon\Carbon;
use Exception;


class OrderController extends Controller
{

    protected $orderProcessor;
    protected $tripCreateFromOrders;
    protected $rateManagement;
    protected $orderStore;
    protected $partyManagementService;

    public function __construct(OrderProcessor $orderProcessor, TripCreateFromOrders $tripCreateFromOrders, RateManagement $rateManagement, OrderStore $orderStore, PartyManagementService $partyManagementService)
    {
        $this->orderProcessor = $orderProcessor;
        $this->tripCreateFromOrders = $tripCreateFromOrders;
        $this->tripCreateFromOrders->setOrderProcessor($orderProcessor);
        $this->rateManagement = $rateManagement;
        $this->rateManagement->setOrderProcessor($orderProcessor);
        $this->orderStore = $orderStore;
        $this->partyManagementService = $partyManagementService;
    }


    public function index(Request $request, $id = null)
    {
        $user = Auth::guard('api')->user();
        if (!$user || !is_object($user)) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized access.',
                'data' => null
            ], 401);
        }
        $org_id = $user->default_org_id ?? $user->org_id ?? $request->input('org_id', 1);
        if (empty($org_id)) {
            $org_id = $user->org_id ?? 0;
            if (empty($org_id)) {
                return response()->json([
                    'status' => false,
                    'message' => 'User organization ID not set.',
                    'data' => null,
                ], 400);
            }
        }

        // Redirect for Carrier business type
        if (isset($user->business_type) && $user->business_type === 'Carrier') {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized access for Carrier.',
                'data' => null,
            ], 403);
        }

        // Get user session data
        $sessionData = [
            'user_id' => $user->user_id ?? $request->input('user_id', 0),
            'cust_id' => $user->cust_id ?? $request->input('cust_id', 0),
            'country_user_ids' => $user->country_user_ids ?? $request->input('country_user_ids', []),
            'be_value' => $user->be_value ?? $request->input('be_value', 0),
            'org_id' => $user->org_id ?? $request->input('org_id', 0),
            'user_role_id' => $user->user_role_id ?? $request->input('user_role_id', 0),
            'sub_cust' => $user->sub_cust ?? $request->input('sub_cust', []),
            'curtz' => (isset($user->usr_tzone) && is_array($user->usr_tzone) && isset($user->usr_tzone['timezone'])) ? $user->usr_tzone['timezone'] : 'UTC',
        ];

        // Ensure critical session data is always set
        if (empty($sessionData['user_id'])) {
            $sessionData['user_id'] = 0;
        }
        if (empty($sessionData['org_id'])) {
            $sessionData['org_id'] = $org_id;
        }
        if (empty($sessionData['country_user_ids']) || !is_array($sessionData['country_user_ids'])) {
            $sessionData['country_user_ids'] = [];
        }
        if (empty($sessionData['sub_cust']) || !is_array($sessionData['sub_cust'])) {
            $sessionData['sub_cust'] = [];
        }
        $orgId = $org_id;

        $data = [
            'postData' => $request->all() ?? [],
        ];

        $getArguments = [];
        $properties = new Properties();
        $order = [];
        $excel_uploaddata = $clexcel_uploaddata = $chexcel_uploaddata = $sxlogin_uploaddata = [];
        $ats_parties = [];
        $whr = $conditions = [];

        if ($id && is_string($id) && in_array($id, ExcelUpload::IDS_FOR_UPLOAD)) {
            try {
                $excel = new ExcelUpload($id);
                $result = $excel->upload($sessionData['org_id']);

                if (!empty($result['booking_ids'])) {
                    try {
                        $properties->getOrderBookings($result['booking_ids'], $ats_parties);
                        $data['ats_parties'] = !empty($ats_parties) ? json_encode($ats_parties, JSON_HEX_TAG) : [];
                    } catch (\Exception $e) {
                        Log::error('Error in getOrderBookings', [
                            'error' => $e->getMessage(),
                            'booking_ids' => $result['booking_ids'],
                        ]);
                    }
                }

                $list_type = $result['list_types']['list_type'] ?? 0;
                $cllist_type = $result['list_types']['cllist_type'] ?? 0;
                $charge_list_type = $result['list_types']['charge_list_type'] ?? 0;
                $sxlogin_list_type = $result['list_types']['sxlogin_list_type'] ?? 0;

                // Add excel upload data to response - use actual data from result
                $data['excel_uploaddata'] = $result['excel_uploaddata'] ?? $excel_uploaddata;
                $data['clexcel_uploaddata'] = $result['clexcel_uploaddata'] ?? $clexcel_uploaddata;
                $data['chexcel_uploaddata'] = $result['chexcel_uploaddata'] ?? $chexcel_uploaddata;
                $data['sxlogin_uploaddata'] = $result['sxlogin_uploaddata'] ?? $sxlogin_uploaddata;
                $data['list_type'] = $list_type;
                $data['cllist_type'] = $cllist_type;
                $data['charge_list_type'] = $charge_list_type;
                $data['sxlogin_list_type'] = $sxlogin_list_type;
            } catch (\Exception $e) {
                Log::error('Error processing Excel upload', [
                    'error' => $e->getMessage(),
                    'id' => $id,
                    'org_id' => $sessionData['org_id'],
                ]);
                // Continue without Excel upload data
            }
        } elseif ($id && is_string($id) && !empty($id)) {
            try {
                $getArguments['bookingid'] = $properties->getBooking($id, $ats_parties);
                $data['getbookingid'] = $getArguments['bookingid'];
                $data['ats_parties'] = !empty($ats_parties) ? json_encode($ats_parties, JSON_HEX_TAG) : [];
            } catch (\Exception $e) {
                Log::error('Error in getBooking', [
                    'error' => $e->getMessage(),
                    'id' => $id,
                ]);
                $getArguments['bookingid'] = [];
            }
        }

        $getArguments = (new GetArgumentsResolver())->resolve($this->getDefaultFilters(), $getArguments, ['page', 'limit']);

        if (isset($getArguments['search_type']) && $getArguments['search_type'] === 'advanced') {
            try {
                $search = new AdvancedSearch($getArguments);
                $conditions = $search->buildWhereClause($request, $sessionData['user_role_id'], $sessionData['user_id'], $sessionData['org_id']);
            } catch (\Exception $e) {
                Log::error('Error creating AdvancedSearch', [
                    'error' => $e->getMessage(),
                    'arguments' => $getArguments,
                ]);
                // Fallback to basic search
                $search = new Search($getArguments);
                $conditions = [];
            }
        } else {
            try {
                $search = new Search($getArguments);
            } catch (\Exception $e) {
                Log::error('Error creating Search', [
                    'error' => $e->getMessage(),
                    'arguments' => $getArguments,
                ]);
                // Create a minimal search object to prevent fatal errors
                $search = new Search([]);
                $conditions = [];
            }
        }

        try {
            $field = $properties->getDateFieldName($getArguments);
            $search->fromDate($field, $whr);
            $search->toDate($field, $whr);
        } catch (\Exception $e) {
            Log::error('Error setting date fields', [
                'error' => $e->getMessage(),
                'arguments' => $getArguments,
            ]);
            // Continue with default date handling
            $field = 'created_at'; // Default field if getDateFieldName fails
        }

        // Additional date field handling for comprehensive search
        if (isset($getArguments['date_field']) && !empty($getArguments['date_field'])) {
            try {
                $customField = $getArguments['date_field'];
                $search->fromDate($customField, $whr);
                $search->toDate($customField, $whr);
            } catch (\Exception $e) {
                Log::error('Error setting custom date field', [
                    'error' => $e->getMessage(),
                    'date_field' => $customField,
                ]);
            }
        }

        // Handle specific date range filters
        if (isset($getArguments['pickup_date_from']) || isset($getArguments['pickup_date_to'])) {
            try {
                $search->fromDate('orders.pickup_datetime', $whr);
                $search->toDate('orders.pickup_datetime', $whr);
            } catch (\Exception $e) {
                Log::error('Error setting pickup date filters', [
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if (isset($getArguments['delivery_date_from']) || isset($getArguments['delivery_date_to'])) {
            try {
                $search->fromDate('orders.delivery_datetime', $whr);
                $search->toDate('orders.delivery_datetime', $whr);
            } catch (\Exception $e) {
                Log::error('Error setting delivery date filters', [
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $orderQueryService = new OrderQueryService();
        try {
            $orderQueryService->addDateRangeFilter($whr, $getArguments, 'advpickupfrom_date', 'advpickupto_date', 'pickup_datetime');
            $orderQueryService->addDateRangeFilter($whr, $getArguments, 'advdeliveryfrom_date', 'advdeliveryto_date', 'delivery_datetime');
        } catch (\Exception $e) {
            Log::error('Error creating OrderQueryService or adding date filters', [
                'error' => $e->getMessage(),
                'arguments' => $getArguments,
            ]);
        }

        try {
            $whr = array_merge($whr, $conditions);
        } catch (\Exception $e) {
            Log::error('Error merging where conditions', [
                'error' => $e->getMessage(),
                'whr' => $whr,
                'conditions' => $conditions,
            ]);
        }

        // Additional search conditions that might have been in the original method
        if (isset($getArguments['searchcustomer_id']) && !empty($getArguments['searchcustomer_id'])) {
            try {
                $properties->getCustomerById($getArguments, $whr, $sessionData['org_id']);
            } catch (\Exception $e) {
                Log::error('Error in getCustomerById', [
                    'error' => $e->getMessage(),
                    'arguments' => $getArguments,
                ]);
            }
        }

        if (isset($getArguments['searchcustomer_name']) && !empty($getArguments['searchcustomer_name'])) {
            try {
                $properties->getCustomerByName($getArguments, $whr);
            } catch (\Exception $e) {
                Log::error('Error in getCustomerByName', [
                    'error' => $e->getMessage(),
                    'arguments' => $getArguments,
                ]);
            }
        }

        if (isset($getArguments['carrier']) && !empty($getArguments['carrier'])) {
            try {
                $properties->getCarrierByName($getArguments, $whr);
            } catch (\Exception $e) {
                Log::error('Error in getCarrierByName', [
                    'error' => $e->getMessage(),
                    'arguments' => $getArguments,
                ]);
            }
        }

        if (isset($getArguments['purchase_order']) && !empty($getArguments['purchase_order'])) {
            try {
                $poResult = $properties->getPurchaseOrder($getArguments, $sessionData['user_role_id'], $sessionData['user_id'], $sessionData['org_id']);
                if ($poResult !== false) {
                    $searchids = [$poResult];
                }
            } catch (\Exception $e) {
                Log::error('Error in getPurchaseOrder', [
                    'error' => $e->getMessage(),
                    'arguments' => $getArguments,
                ]);
            }
        }

        $status_search = $getArguments['status'] ?? $getArguments['order_status'] ?? '';

        // Initialize searchids with default value to prevent undefined variable errors
        $searchids = $getArguments['bookingid'] ?? [];

        if (empty($searchids) && !empty($getArguments['container_no'])) {
            try {
                $searchids = OrderReference::getContainerNum($sessionData['user_id'], $sessionData['country_user_ids'], $sessionData['be_value'], $getArguments['container_no']);
            } catch (\Exception $e) {
                Log::error('Error in getContainerNum', [
                    'error' => $e->getMessage(),
                    'container_no' => $getArguments['container_no'],
                ]);
            }
        } elseif (!empty($searchids) && !empty($getArguments['container_no'])) {
            try {
                $container_num_arr = OrderReference::getContainerNum($sessionData['user_id'], $sessionData['country_user_ids'], $sessionData['be_value'], '');
                if (is_array($container_num_arr) && is_array($searchids)) {
                    $searchids = array_intersect($container_num_arr, $searchids);
                }
            } catch (\Exception $e) {
                Log::error('Error in getContainerNum intersection', [
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if (!empty($getArguments['order_reftype'])) {
            try {
                $advancedids = OrderReference::getRefNum($sessionData['user_id'], $getArguments['order_reftype'], $getArguments['ref_val']);
                $searchids = !empty($advancedids) ? $advancedids : [0];
            } catch (\Exception $e) {
                Log::error('Error in getRefNum', [
                    'error' => $e->getMessage(),
                    'order_reftype' => $getArguments['order_reftype'],
                    'ref_val' => $getArguments['ref_val'],
                ]);
            }
        }

        if (!empty($getArguments['salog_ref'])) {
            try {
                $advancedids = OrderReference::getSalogRefNum($sessionData['user_id'], $getArguments['salog_ref'], $sessionData['org_id']);
                $searchids = empty($advancedids) ? [0] : $advancedids;
            } catch (\Exception $e) {
                Log::error('Error in getSalogRefNum', [
                    'error' => $e->getMessage(),
                    'salog_ref' => $getArguments['salog_ref'],
                ]);
            }
        }

        $wildcard_search = $getArguments['wildcard_order_id'] ?? '';
        if (!empty($getArguments['order_id'])) {
            $searchids = [$getArguments['order_id']];
        }

        if (!empty($getArguments['order_references'])) {
            try {
                $referenceResults = OrderReference::getOrderIdsByReferenceValues($getArguments['order_references']);
                $searchids = empty($referenceResults) ? [0] : $referenceResults;
            } catch (\Exception $e) {
                Log::error('Error in getOrderIdsByReferenceValues', [
                    'error' => $e->getMessage(),
                    'order_references' => $getArguments['order_references'],
                ]);
            }
        }

        // Handle wildcard search if provided
        if (!empty($wildcard_search)) {
            try {
                // Use existing getRefNum method for wildcard search since getOrderIdsByWildcard doesn't exist
                $wildcardResults = OrderReference::getRefNum($sessionData['user_id'], 'DQ', $wildcard_search);
                if (!empty($wildcardResults) && is_array($wildcardResults)) {
                    if (empty($searchids) || !is_array($searchids)) {
                        $searchids = $wildcardResults;
                    } else {
                        $searchids = array_intersect($wildcardResults, $searchids);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error in wildcard search', [
                    'error' => $e->getMessage(),
                    'wildcard_search' => $wildcard_search,
                ]);
            }
        }

        // IMPORTANT: If no specific search criteria provided, don't filter by searchids
        // This ensures we get all orders when no search is specified
        $useSearchIds = !empty($searchids) && is_array($searchids) && !in_array(0, $searchids);

        try {
            $orderIdsMap = $properties->getOrderIdsMapping($wildcard_search, $searchids, $sessionData['org_id'], $sessionData['user_id'], $sessionData['country_user_ids']);
        } catch (\Exception $e) {
            Log::error('Error in getOrderIdsMapping', [
                'error' => $e->getMessage(),
                'wildcard_search' => $wildcard_search,
                'searchids' => $searchids,
            ]);
            $orderIdsMap = [];
        }

        $subcusts = $sessionData['sub_cust'];
        if ($sessionData['user_role_id'] == '4' && is_array($subcusts) && !empty($subcusts)) {
            $subcusts[] = $sessionData['cust_id'];
        }

        // Only pass searchids if we have valid search criteria, otherwise pass null to get all orders
        try {
            $orderdataQuery = $orderQueryService->indexQuery(
                $sessionData['user_id'],
                $orgId,
                $useSearchIds ? $searchids : null,
                $status_search,
                $sessionData['cust_id'],
                $sessionData['country_user_ids'],
                $whr,
                $subcusts,
                $orderIdsMap
            );

            // Validate that we have a proper query
            if (!$orderdataQuery) {
                throw new \Exception('Failed to build order query');
            }
        } catch (\Exception $e) {
            Log::error('Error building order query', [
                'error' => $e->getMessage(),
                'user_id' => $sessionData['user_id'],
                'org_id' => $orgId,
                'searchids' => $searchids,
                'use_search_ids' => $useSearchIds,
            ]);
            throw $e;
        }

        // Handle pagination using standard Laravel pagination (same as other controllers)
        $perPage = max(1, (int) ($request->input('per_page', 15)));
        $page = max(1, (int) ($request->input('page', 1)));

        // Apply pagination directly to the query (standard pattern)
        try {
            $orders = $orderdataQuery->orderBy('id')->paginate($perPage, ['*'], 'page', $page);
            $total = $orders->total();
        } catch (\Exception $e) {
            Log::error('Error executing pagination', [
                'error' => $e->getMessage(),
                'user_id' => $sessionData['user_id'],
                'org_id' => $orgId,
                'per_page' => $perPage,
                'page' => $page,
            ]);
            throw $e;
        }
        $fallbackUsed = false;

        // Fallback: If no results and no specific search criteria, try a basic query
        if ($total === 0 && !$useSearchIds && (empty($whr) || !is_array($whr) || count($whr) === 0)) {
            Log::warning('Main query returned no results, trying fallback query', [
                'user_id' => $sessionData['user_id'],
                'org_id' => $orgId,
                'searchids' => $searchids,
                'whr' => $whr,
            ]);

            try {
                // Try a simple query to get basic orders
                $fallbackQuery = \App\Models\Order::where('status', '!=', 0);
                if ($orgId) {
                    $fallbackQuery->where('org_id', $orgId);
                }
                if ($sessionData['user_id']) {
                    $fallbackQuery->where('user_id', $sessionData['user_id']);
                }

                $orders = $fallbackQuery->orderBy('id')->paginate($perPage, ['*'], 'page', $page);
                $total = $orders->total();
                $fallbackUsed = true;

                Log::info('Fallback query result', [
                    'total' => $total,
                    'fallback_used' => true,
                ]);
            } catch (\Exception $e) {
                Log::error('Error executing fallback query', [
                    'error' => $e->getMessage(),
                    'user_id' => $sessionData['user_id'],
                    'org_id' => $orgId,
                ]);
                throw $e;
            }
        }

        // Process order items if we have results and not in debug mode
        if ($orders && $orders->count() > 0 && !$request->query('debug')) {
            try {
                // Create PaginationResponse object from Laravel pagination result
                $paginationResponse = new \App\Services\OrderList\PaginationResponse(
                    $orders->items(),
                    $orders->total(),
                    $orders->currentPage(),
                    $orders->perPage()
                );
                $this->orderProcessor->processOrderItems($paginationResponse, $properties, $sessionData);
            } catch (\Exception $e) {
                Log::error('Error processing order items', [
                    'error' => $e->getMessage(),
                    'user_id' => $sessionData['user_id'],
                    'org_id' => $orgId,
                ]);
                // Don't throw here, continue with unprocessed orders
            }
        }

        // Log successful retrieval for debugging
        Log::info('Orders retrieved successfully', [
            'user_id' => $sessionData['user_id'],
            'org_id' => $sessionData['org_id'],
            'total_records' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'search_conditions_count' => is_array($whr) ? count($whr) : 0,
            'searchids_count' => is_array($searchids) ? count($searchids) : 0,
            'searchids_content' => $searchids,
            'use_search_ids' => $useSearchIds,
            'date_field_used' => $field,
            'search_type' => $getArguments['search_type'] ?? 'basic',
            'excel_upload_id' => $id,
            'has_ats_parties' => !empty($ats_parties),
            'org_id_used' => $orgId,
            'status_search' => $status_search,
        ]);

        // Prepare additional data for response
        $additionalData = [
            'pagination' => $orders,
            'search_conditions' => $whr,
            'search_arguments' => $getArguments,
            'status_search' => $status_search,
            'searchids' => $searchids,
            'orderIdsMap' => $orderIdsMap,
            'subcusts' => $subcusts,
            'date_field_used' => $field,
            'search_type' => $getArguments['search_type'] ?? 'basic',
            'total_search_conditions' => is_array($whr) ? count($whr) : 0,
            'debug_info' => [
                'use_search_ids' => $useSearchIds,
                'searchids_count' => is_array($searchids) ? count($searchids) : 0,
                'searchids_content' => $searchids,
                'org_id_used' => $orgId,
                'user_id_used' => $sessionData['user_id'],
                'whr_conditions' => $whr,
                'fallback_used' => $fallbackUsed,
            ],
            'session_data' => [
                'user_id' => $sessionData['user_id'],
                'org_id' => $sessionData['org_id'],
                'user_role_id' => $sessionData['user_role_id'],
                'business_type' => $user->business_type ?? null,
                'be_value' => $sessionData['be_value'],
                'country_user_ids' => $sessionData['country_user_ids'],
            ],
        ];

        // Check if data is empty
        if (!$orders || $orders->count() === 0) {
            return response()->json([
                'status' => true,
                'message' => 'No records found for the specified type and organization',
                'data' => [
                    'page' => 1,
                    'perPage' => $perPage,
                    'total' => 0,
                    'records' => [],
                ],
            ], 200);
        }

        // Return success response with consistent structure (same as other controllers)
        return response()->json([
            'status' => true,
            'message' => 'Orders retrieved successfully',
            'data' => [
                'page' => $orders ? $orders->currentPage() : 1,
                'perPage' => $orders ? $orders->perPage() : $perPage,
                'total' => $total,
                'records' => $orders ? $orders->items() : [],
                'additionalData' => $additionalData,
            ],
        ], 200);
    }

    protected function getDefaultFilters(): array
    {
        return [
            'status' => '',
            'order_status' => '',
            'search_type' => 'basic',
            'container_no' => '',
            'order_reftype' => '',
            'ref_val' => '',
            'salog_ref' => '',
            'order_id' => '',
            'order_references' => [],
            'wildcard_order_id' => '',
            'advpickupfrom_date' => '',
            'advpickupto_date' => '',
            'advdeliveryfrom_date' => '',
            'advdeliveryto_date' => '',
            'date_field' => '',
            'pickup_date_from' => '',
            'pickup_date_to' => '',
            'delivery_date_from' => '',
            'delivery_date_to' => '',
            'searchcustomer_id' => '',
            'searchcustomer_name' => '',
            'carrier' => '',
            'purchase_order' => '',
            'per_page' => 15,
            'page' => 1,
        ];
    }

    public function neworder(Request $request)
    {
        $post = $request->all() ?? [];
        $user = Auth::user();
        $user_id = $user->user_id ?? $post['user_id'] ?? 0;
        $org_id = $user->org_id ?? $post['org_id'] ?? 0;
        $be_value = $user->be_value ?? $post['be_value'] ?? 0;

        $currencies = $user->usr_tzone['currency'] ?? [];
        $country_masters = CountryMaster::where('status', 1)->pluck('currency')->toArray();
        $currencies = array_unique(array_merge($currencies, $country_masters));

        $pickup_details = [];
        $cust_id = $user->cust_id ?? null;
        if ($cust_id) {
            $pickup = $this->partyManagementService->getPickupDetails($cust_id);
            $pickup_details = is_array($pickup) ? ($pickup['data'] ?? []) : [];
        }

        // Get order types
        $ordertypes = [];
        if ($cust_id) {
            $ordertypes = OrderType::where('customer_id', $cust_id)
                ->where('org_id', $org_id)
                ->where('status', 1)
                ->select('id', 'type_name')
                ->groupBy('type_name', 'id')
                ->get()
                ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                ->toArray();

            if (empty($ordertypes)) {
                $ordertypes = OrderType::where('org_id', $org_id)
                    ->where('status', 1)
                    ->select('id', 'type_name')
                    ->groupBy('id', 'type_name')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();

                if (empty($ordertypes)) {
                    $ordertypes = OrderType::where('org_id', 'SGKN')
                        ->where('status', 1)
                        ->select('id', 'type_name')
                        ->groupBy('id', 'type_name')
                        ->get()
                        ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                        ->toArray();
                }
            }
        } else {
            $branch_exists = OrderType::where('status', 1)
                ->where('be_value', $be_value)
                ->where('org_id', $org_id)
                ->exists();

            if ($branch_exists) {
                $ordertypes = OrderType::where('status', 1)->where('org_id', $org_id)
                    ->select('id', 'type_name')
                    ->groupBy('type_name', 'id')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();
            } else {
                $ordertypes = OrderType::where('status', 1)
                    ->where('org_id', $org_id)
                    ->select('id', 'type_name')
                    ->groupBy('type_name', 'id')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();
            }
        }

        $roles = $this->partyManagementService->getPartyTypes($org_id);

        // Get charge codes
        $chargecodes = ChargeCode::where('status', 1)
            ->select('id', 'charge_code')
            ->get()
            ->map(fn($charge) => ['charge_id' => $charge->id, 'charge_code' => $charge->charge_code])
            ->toArray();

        // Get VAT categories
        $vatcategory = VatCategory::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'description', 'vat_category', 'vat_percentage')
            ->get()
            ->map(fn($vat) => [
                'id' => $vat->id,
                'val' => "{$vat->id}_{$vat->vat_category}",
                'desc' => "{$vat->description} ({$vat->vat_category}-{$vat->vat_percentage})",
            ])
            ->toArray();

        // Get cost centers
        $costcenter = CostCenter::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'type_name')
            ->groupBy('type_name', 'id')
            ->get()
            ->map(fn($cost) => ['type_id' => $cost->id, 'type_name' => $cost->type_name])
            ->toArray();

        // Prepare response data
        $data = [
            'currencies' => $currencies,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'chargecodes' => $chargecodes,
            'pickup_details' => $pickup_details,
            'ordertypes' => $ordertypes,
            'costcenter' => $costcenter,
            'roles' => $roles,
            'vatcategory' => $vatcategory,
            'marks_numbers_column' => true,
        ];

        return response()->json([
            'status' => 'success',
            'message' => 'Order data retrieved successfully',
            'data' => $data
        ], 200);
    }

    public function store(StoreOrderRequest $request)
    {
        // Initialize variables for proper scope
        $org_id = null;
        $user_id = null;
        $be_value = null;
        $user = null;

        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $org_id = $user->default_org_id;

            if (empty($org_id)) {
                return response()->json([
                    'status' => false,
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            $user_id = $user->id;
            $be_value = $user->be_value ?? 1;
            $curtz = $user->usr_tzone['timezone'] ?? 'UTC';
            $cdate = Carbon::now()->format('Y-m-d H:i:s');

            // Validation is automatically handled by StoreOrderRequest
            $data = $request->validated();

            // Extract validated data with defaults
            $product = $data['product'] ?? '';
            $service = $data['service'] ?? '';
            $shipper_id = $data['shipper_id'] ?? '';
            $delivery_terms = $data['delivery_terms'] ?? '';
            $incoterm = $data['incoterm'] ?? '';
            $shipment_id = $data['delivery_note'] ?? 'SX' . time();
            $container_no = $data['container_num'] ?? '';
            $porder = $data['purchase_order'] ?? '';
            $notify_party = $data['notify_party'] ?? '';
            $currency = $data['currency'] ?? '';
            $goods_value = $data['goods_value'] ?? 0.00;
            $external_order_id = $data['external_order_id'] ?? '';
            $shipment_type = $data['shipment_type'] ?? 0;
            $region = (int)($data['region'] ?? 0);
            $p_latitude = $data['p_latitude'] ?? '';
            $p_longitude = $data['p_longitude'] ?? '';
            $d_latitude = $data['d_latitude'] ?? '';
            $d_longitude = $data['d_longitude'] ?? '';
            $party_row_id = $data['order_party_row_id'] ?? '0';
            $order_inv_row_id = $data['order_inv_row_id'] ?? '0';
            $order_cargo_id = $data['order_cargo_id'] ?? '';
            $pickup = $data['order_pickup_id'] ?? '0';
            $delivery = $data['consignee_id'] ?? '';
            $early_pickup = $data['estimated_early_pickup'] ?? '';
            $late_pickup = $data['estimated_late_pickup'] ?? '';
            $early_delivery = $data['estimated_early_delivery'] ?? '';
            $late_delivery = $data['estimated_late_delivery'] ?? '';
            $modeof_trasnport = $data['mode_of_transport'] ?? 'LTL';
            $order_type = $data['order_type'] ?? '';
            $cost_center = $data['cost_center'] ?? null;
            $rev_row_id = $data['rev_row_id'] ?? '';
            $ordcost_row_id = $data['ordcost_row_id'] ?? '';
            $customer_code = $data['customer_id'] ?? '';
            $driver_pickup_instructions = $data['driver_pickup_instructions'] ?? '';
            $driver_delivery_instructions = $data['driver_delivery_instructions'] ?? '';
            $multiple_marks_numbers = $data['multiple_marks_numbers'] ?? '';
            $multiple_marks_numbers = str_replace(["\r\n", "\r", "\n"], ", ", $multiple_marks_numbers);
            $docs_sent_datetime = $data['docs_sent_datetime'] ?? '';
            $docs_received_datetime = $data['docs_received_datetime'] ?? '';
            $third_party_post = $data['third_party_post'] ?? [];
            $third_party_post_str = !empty($third_party_post) ? implode(',', $third_party_post) : '';

            // Extract new General Info fields
            $cust_do = $data['cust_do'] ?? '';
            $custref_po = $data['custref_po'] ?? '';
            $original_document_sent = $data['original_document_sent'] ?? '';
            $original_document_received = $data['original_document_received'] ?? '';
            $load_board_options = $data['load_board_options'] ?? '';

            // Extract new Shipper fields
            $shipper_name = $data['shipper_name'] ?? '';
            $shipper_street = $data['shipper_street'] ?? '';
            $shipper_city = $data['shipper_city'] ?? '';
            $shipper_province = $data['shipper_province'] ?? '';
            $shipper_country = $data['shipper_country'] ?? '';
            $shipper_zipcode = $data['shipper_zipcode'] ?? '';
            $shipper_phone = $data['shipper_phone'] ?? '';
            $shipper_fax = $data['shipper_fax'] ?? '';
            $shipper_email = $data['shipper_email'] ?? '';

            // Extract new Consignee fields
            $consignee_name = $data['consignee_name'] ?? '';
            $consignee_street = $data['consignee_street'] ?? '';
            $consignee_city = $data['consignee_city'] ?? '';
            $consignee_province = $data['consignee_province'] ?? '';
            $consignee_country = $data['consignee_country'] ?? '';
            $consignee_zipcode = $data['consignee_zipcode'] ?? '';
            $consignee_phone = $data['consignee_phone'] ?? '';
            $consignee_fax = $data['consignee_fax'] ?? '';
            $consignee_email = $data['consignee_email'] ?? '';

            // Handle cost center lookup - convert string code to integer ID
            $cost_center_id = null;
            if (!empty($cost_center)) {
                if (is_numeric($cost_center)) {
                    // If it's already a numeric ID, use it directly
                    $cost_center_id = (int)$cost_center;
                } else {
                    // If it's a string code, look up the ID
                    $costCenterObj = CostCenter::where('type_name', $cost_center)
                        ->where('org_id', $org_id)
                        ->where('status', 1)
                        ->first();
                    $cost_center_id = $costCenterObj ? $costCenterObj->id : null;
                }
            }

            // Process pickup and delivery times
            $pickup_times = $this->orderProcessor->processOrderDatetimes($early_pickup, $late_pickup);
            $e_pickup = $this->orderProcessor->getDateTimeByTimezone('UTC', $pickup_times['early'], $curtz)['datetime'];
            $l_pickup = $this->orderProcessor->getDateTimeByTimezone('UTC', $pickup_times['late'], $curtz)['datetime'];

            $delivery_times = $this->orderProcessor->processOrderDatetimes($early_delivery, $late_delivery);
            $e_delivery = $this->orderProcessor->getDateTimeByTimezone('UTC', $delivery_times['early'], $curtz)['datetime'];
            $l_delivery = $this->orderProcessor->getDateTimeByTimezone('UTC', $delivery_times['late'], $curtz)['datetime'];

            // Get party details
            $drop_id = $pickup_custid = 0;
            $pickup_name = $pickup_country = $pickup_street = $pickup_pincode = $pickup_city = $pickup_state = '';
            $drop_name = $drop_country = $drop_street = $drop_pincode = $drop_city = $drop_state = '';
            $pickup_latitude = $pickup_longitude = $drop_latitude = $drop_longitude = $pickup_address = $drop_address = '';

            // Resolve party IDs - handle both integer IDs and string codes
            try {
                $delivery_party_id = $this->partyManagementService->resolvePartyId($delivery, $org_id, 'Consignee');
                $shipper_party_id = $this->partyManagementService->resolvePartyId($shipper_id, $org_id, 'Shipper');
            } catch (\Exception $e) {
                $field = strpos($e->getMessage(), 'Consignee') !== false ? 'consignee_id' : 'shipper_id';
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage(),
                    'data' => null,
                    'errors' => [$field => 'Invalid party code'],
                ], 422);
            }

            // Get party details using resolved IDs
            if ($delivery_party_id) {
                $drop_details = $this->partyManagementService->getPartyDetailsOptimized($delivery_party_id);
                if ($drop_details) {
                    $drop_id = $drop_details->customeridentifier ?? $drop_details->code;
                    $drop_name = $drop_details->name;
                    $drop_state = $drop_details->state;
                    $drop_country = $drop_details->country;
                    $drop_street = $drop_details->street;
                    $drop_pincode = $drop_details->pincode;
                    $drop_city = $drop_details->city;
                    $drop_latitude = $drop_details->latitude;
                    $drop_longitude = $drop_details->longitude;
                    $drop_mobile = $drop_details->mobile;
                }
            }

            if ($shipper_party_id) {
                $shipper_details = $this->partyManagementService->getPartyDetailsOptimized($shipper_party_id);
                if ($shipper_details) {
                    $pickup_custid = $shipper_details->customeridentifier ?? $shipper_details->code;
                    $pickup_name = $shipper_details->name;
                    $pickup_state = $shipper_details->state;
                    $pickup_country = $shipper_details->country;
                    $pickup_street = $shipper_details->street;
                    $pickup_pincode = $shipper_details->pincode;
                    $pickup_city = $shipper_details->city;
                    $pickup_latitude = $shipper_details->latitude;
                    $pickup_longitude = $shipper_details->longitude;
                    $pickup_mobile = $shipper_details->mobile;
                }
            }

            // Determine latitude and longitude
            $lat1 = $lng1 = $lat2 = $lng2 = '';
            if ($pickup_latitude && $pickup_longitude && $drop_latitude && $drop_longitude) {
                $lat1 = $pickup_latitude;
                $lng1 = $pickup_longitude;
                $lat2 = $drop_latitude;
                $lng2 = $drop_longitude;
            } elseif ($p_latitude && $p_longitude && $d_latitude && $d_longitude) {
                $lat1 = $p_latitude;
                $lng1 = $p_longitude;
                $lat2 = $d_latitude;
                $lng2 = $d_longitude;
            } else {
                $add1 = implode(',', array_filter([$pickup_street, $pickup_city, $pickup_country, $pickup_pincode]));
                $add2 = implode(',', array_filter([$drop_street, $drop_city, $drop_country, $drop_pincode]));
                $data1 = $this->orderProcessor->getLatLngsByPlace($add1);
                $lat1 = $data1[0] ?? '';
                $lng1 = $data1[1] ?? '';
                $data2 = $this->orderProcessor->getLatLngsByPlace($add2);
                $lat2 = $data2[0] ?? '';
                $lng2 = $data2[1] ?? '';
            }

            // Get transport mode
            $transport_mode = TransportMode::where('code', $modeof_trasnport)->select('id', 'name')->first();
            $tid = $transport_mode->id ?? 0;
            $tname = $transport_mode->name ?? '';

            // Determine customer_id
            $customer_id = $pickup ? (int)$pickup : 0;
            if (!$customer_id && $customer_code) {
                $customer = $this->partyManagementService->getCustomerByCode($customer_code, $user_id);
                $customer_id = $customer->id ?? 0;
            }

            // Get timezone-converted dates
            $logdateArr = $this->orderProcessor->getDateTimeByTimezone('UTC', Carbon::now()->format('Y-m-d H:i:s'), $curtz);
            $logdate = is_array($logdateArr) ? ($logdateArr['datetime'] ?? Carbon::now()->format('Y-m-d H:i:s')) : Carbon::now()->format('Y-m-d H:i:s');
            $docs_sent_datetime_arr = ($docs_sent_datetime && $docs_sent_datetime !== '0000-00-00 00:00:00') ? $this->orderProcessor->getDateTimeByTimezone('UTC', $docs_sent_datetime, $curtz) : [];
            $docs_sent_datetime = is_array($docs_sent_datetime_arr) ? ($docs_sent_datetime_arr['datetime'] ?? '') : '';
            $docs_received_datetime_arr = ($docs_received_datetime && $docs_received_datetime !== '0000-00-00 00:00:00') ? $this->orderProcessor->getDateTimeByTimezone('UTC', $docs_received_datetime, $curtz) : [];
            $docs_received_datetime = is_array($docs_received_datetime_arr) ? ($docs_received_datetime_arr['datetime'] ?? '') : '';

            // Child ID from session
            $childid = $user->childid ?? 0;

            // Created source logic
            $created_source = '4';
            // Add logic for be_value == 'INCL' if needed
            // if ($be_value == 'INCL') { ... }

            // Generate booking ID first (outside transaction to avoid abort issues)
            $user_data = User::where('id', $user_id)->select('country_code', 'default_org_id')->first();
            $country_code = $user_data->country_code ?? '';
            $bookingInfo = ['user_id' => $user_id, 'order_id' => 0, 'country_code' => $country_code, 'org_id' => $org_id];
            $booking_id = $this->orderProcessor->generateBookingId($bookingInfo);

            DB::beginTransaction();

            // Insert order
            $orderinfo = [
                'shipment_id' => 0,
                'customer_id' => $customer_id,
                'product' => $product,
                'pickup_datetime' => $e_pickup,
                'delivery_datetime' => $e_delivery,
                'pickup_endtime' => $l_pickup,
                'drop_endtime' => $l_delivery,
                'goods_value' => $goods_value,
                'currency' => $currency,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'created_at' => $logdate,
                'drop_custid' => $drop_id,
                'drop_partyid' => $drop_id,
                'user_id' => $user_id,
                'sub_uid' => $childid,
                'pickup_custid' => $pickup_custid,
                'pickup_partyid' => $pickup_custid,
                'pickup_country' => $pickup_country,
                'pickup_city' => $pickup_city,
                'pickup_pincode' => $pickup_pincode,
                'pickup_company' => $pickup_name,
                'pickup_address1' => $pickup_street,
                'pickup_address2' => $pickup_state,
                'delivery_country' => $drop_country,
                'delivery_city' => $drop_city,
                'delivery_pincode' => $drop_pincode,
                'delivery_company' => $drop_name,
                'delivery_address1' => $drop_street,
                'delivery_address2' => $drop_state,
                'is_created' => '1',
                'plat' => $lat1,
                'plng' => $lng1,
                'dlat' => $lat2,
                'dlng' => $lng2,
                'transport_mode' => $modeof_trasnport,
                'created_source' => $created_source,
                'external_order_id' => $external_order_id,
                'shipment_type' => $shipment_type,
                'region' => $region,
                'third_party_post' => $third_party_post_str,

                // New General Info fields
                'cust_do' => $cust_do,
                'custref_po' => $custref_po,
                'original_document_sent' => $original_document_sent ?: null,
                'original_document_received' => $original_document_received ?: null,
                'load_board_options' => !empty($load_board_options) ? json_encode($load_board_options) : null,

                // New Shipper fields
                'shipper_name' => $shipper_name,
                'shipper_street' => $shipper_street,
                'shipper_city' => $shipper_city,
                'shipper_province' => $shipper_province,
                'shipper_country' => $shipper_country,
                'shipper_zipcode' => $shipper_zipcode,
                'shipper_phone' => $shipper_phone,
                'shipper_fax' => $shipper_fax,
                'shipper_email' => $shipper_email,

                // New Consignee fields
                'consignee_name' => $consignee_name,
                'consignee_street' => $consignee_street,
                'consignee_city' => $consignee_city,
                'consignee_province' => $consignee_province,
                'consignee_country' => $consignee_country,
                'consignee_zipcode' => $consignee_zipcode,
                'consignee_phone' => $consignee_phone,
                'consignee_fax' => $consignee_fax,
                'consignee_email' => $consignee_email,

                // Party ID fields - store in orders table
                'shipper_id' => $shipper_id,
                'consignee_id' => $delivery,

                // Cargo fields are now handled separately in order_cargodetails table
            ];

            // Handle ID generation to avoid sequence conflicts
            $nextId = $this->partyManagementService->generateNextId(Order::class, 'orders');
            $orderinfo['id'] = $nextId;
            $orderinfo['updated_at'] = now();

            DB::table('orders')->insert($orderinfo);

            // Fetch the created record
            $order = Order::find($nextId);
            $order_id = $order->id;

            // Update order with booking ID
            $order->update(['order_id' => $booking_id]);

            // Insert order details
            $details = [
                'service' => $service,
                'delivery_term' => $delivery_terms,
                'incoterm' => $incoterm,
                'notify_party' => $notify_party,
                'order_row_id' => $order_id,
                'order_id' => $booking_id,
                'created_at' => $logdate,
                'order_type' => $order_type,
                'cost_center_id' => $cost_center_id,
                'docs_received_datetime' => $docs_received_datetime ?: null,
                'docs_sent_datetime' => $docs_sent_datetime ?: null,
                'temperature_control' => '0',
                'valorance_insurance' => '0',
                'high_cargo_value' => '0',
                'customs_required' => '0',
                'user_id' => $user_id,
                'be_value' => $be_value,
                'order_id' => $booking_id, // Use booking_id as the order_id string
            ];

            // Handle ID generation to avoid sequence conflicts
            $nextId = $this->partyManagementService->generateNextId(OrderDetail::class, 'order_details');
            $details['id'] = $nextId;
            $details['updated_at'] = now();

            DB::table('order_details')->insert($details);

            // Handle cargo details using existing cargo management system
            // Cargo details will be handled separately via existing cargo APIs:
            // - POST /api/orders/items-list (for item selection popup)
            // - POST /api/orders/items-details (for item details)
            // - POST /api/orders/cargo-save (for saving cargo details)
            // - GET /api/orders/cargodetails-listing (for listing cargo details)

            $this->orderProcessor->insertOrdersRefFileLineIdentifier([
                'pickupCity' => $pickup_city,
                'pickupState' => $pickup_state,
                'pickupCountry' => $pickup_country,
                'dropCity' => $drop_city,
                'dropState' => $drop_state,
                'dropCountry' => $drop_country,
                'orgId' => $org_id,
                'beValue' => $be_value,
                'orderRowId' => $order_id,
                'date' => $logdate,
            ]);

            $shipperadd_id = $this->partyManagementService->createOrderPartyAddress(
                $order_id,
                $be_value,
                $user_id,
                $cdate,
                $shipper_id,
                $pickup_city,
                $pickup_street,
                $pickup_state,
                $pickup_address,
                $pickup_pincode,
                $pickup_country
            );

            $dropadd_id = $this->partyManagementService->createOrderPartyAddress(
                $order_id,
                $be_value,
                $user_id,
                $cdate,
                $delivery,
                $drop_city,
                $drop_street,
                $drop_state,
                $drop_address,
                $drop_pincode,
                $drop_country
            );

            // Handle cargo details
            $cargo_forship = [];
            try {
                $cargo_forship = $this->orderStore->createCargoDetails($order_id, $be_value, $user_id, $cdate, $order_cargo_id);
            } catch (\Exception $e) {
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage(),
                    'data' => null,
                    'errors' => ['order_cargo_id' => 'Invalid cargo ID'],
                ], 422);
            }

            $unitspec = !empty($cargo_forship) ? implode(',', $cargo_forship) : '1';

            $totals = OrderCargoDetail::where('order_id', $order_id)->where('status', true)->selectRaw('COALESCE(SUM(weight), 0) as total_weight, COALESCE(SUM(volume), 0) as total_volume, COALESCE(SUM(quantity), 0) as total_quantity')->first();

            // Initialize cargo array for route creation
            $cargo = [];
            $order->update([
                'volume' => $totals->total_volume,
                'weight' => $totals->total_weight,
                'quantity' => $totals->total_quantity,
            ]);

            // Insert order references
            $this->orderStore->createOrderReference($order_id, $be_value, $user_id, $cdate, 'PO', $porder);
            $this->orderStore->createOrderReference($order_id, $be_value, $user_id, $cdate, 'ORD_PIKINST', $driver_pickup_instructions);
            $this->orderStore->createOrderReference($order_id, $be_value, $user_id, $cdate, 'ORD_DLVINST', $driver_delivery_instructions);
            $this->orderStore->createOrderReference($order_id, $be_value, $user_id, $cdate, 'MARKS_NUMBERS', $multiple_marks_numbers);

            // Handle order parties
            $ids = $party_row_id !== '0' ? array_filter(explode(',', $party_row_id)) : [];

            foreach ($ids as $id) {
                if ($id) {
                    try {
                        $party_id = $this->partyManagementService->resolvePartyId($id, $org_id, 'Party');
                        $party_type = $this->partyManagementService->getPartyTypeById($party_id);
                        $this->partyManagementService->createOrderParty($order_id, $be_value, $user_id, $cdate, $booking_id, $party_id, $party_type);
                    } catch (\Exception $e) {
                        return response()->json([
                            'status' => false,
                            'message' => $e->getMessage(),
                            'data' => null,
                            'errors' => ['order_party_row_id' => 'Invalid party code'],
                        ], 422);
                    }
                }
            }

            // Handle invoice references
            if ($order_inv_row_id && $order_inv_row_id !== '0') {
                $inv_ids = array_filter(explode(',', $order_inv_row_id));
                foreach ($inv_ids as $inv_id) {
                    if ($inv_id) {
                        $this->orderStore->createOrderReference($order_id, $be_value, $user_id, $cdate, 'INV', $inv_id);
                    }
                }
            }

            // Sub customer parties
            $sub_cut_parties = [$shipper_id, $delivery, '0', '0'];
            $this->orderProcessor->subcustpartiesinsert($order_id, $booking_id, $sub_cut_parties, $org_id, $be_value, $user_id, $cdate);

            // Handle revenue updates
            $rev_ids = $rev_row_id && $rev_row_id !== '0' ? array_filter(explode(',', $rev_row_id)) : [];
            if ($ordcost_row_id) {
                $cost_ids = array_filter(explode(',', $ordcost_row_id));
                $rev_ids = array_merge($rev_ids, $cost_ids);
            }

            // Filter out non-numeric IDs to avoid type errors
            $numeric_rev_ids = array_filter($rev_ids, function ($id) {
                return is_numeric($id);
            });

            if ($numeric_rev_ids) {
                Revenue::whereIn('id', $numeric_rev_ids)->update(['order_id' => $order_id]);
            }

            // Customer details
            $customer_details = $this->partyManagementService->getCustomerDetailsById($customer_id);
            $customer_email = $customer_details->email ?? '';
            $customer_phone = $customer_details->mobile ?? '';

            // Geocode and shipment
            $pickupinfo = [
                'country' => trim($pickup_country),
                'order_country' => trim($pickup_country),
                'order_city' => trim($pickup_city),
                'order_zipcode' => trim($pickup_pincode),
                'state' => trim($pickup_state),
                'city' => trim($pickup_city),
                'region' => trim($pickup_street),
                'zipcode' => trim($pickup_pincode),
                'stoptype' => 'P',
            ];

            $dropinfo = [
                'country' => trim($drop_country),
                'order_country' => trim($drop_country),
                'order_city' => trim($drop_city),
                'order_zipcode' => trim($drop_pincode),
                'state' => trim($drop_state),
                'city' => trim($drop_city),
                'region' => trim($drop_street),
                'zipcode' => trim($drop_pincode),
                'stoptype' => 'D',
            ];

            $pickupgeocode = $this->orderProcessor->checkgeocode($pickupinfo);
            $dropgeocode = $this->orderProcessor->checkgeocode($dropinfo);

            if ($pickupgeocode && $dropgeocode) {
                $pickupgeocode['stoptype'] = 'P';
                $dropgeocode['stoptype'] = 'D';
                $pickupgeocode['order_country'] = trim($pickup_country);
                $pickupgeocode['order_city'] = trim($pickup_city);
                $pickupgeocode['order_zipcode'] = trim($pickup_pincode);
                $dropgeocode['order_country'] = trim($drop_country);
                $dropgeocode['order_city'] = trim($drop_city);
                $dropgeocode['order_zipcode'] = trim($drop_pincode);
                $pickupgeocode['cargo'] = $cargo ?? [];
                $dropgeocode['cargo'] = $cargo ?? [];
                $pickuproute = $this->orderProcessor->getcust_routeautomate($customer_id, $pickupgeocode);
                $droproute = $this->orderProcessor->getcust_routeautomate($customer_id, $dropgeocode);

                if ($pickuproute && $droproute) {
                    $orderinfo = [
                        'id' => $order_id,
                        'order_id' => $booking_id,
                        'shipment_name' => 'BOXES',
                        'customer_phone' => $customer_phone,
                        'customer_email' => $customer_email,
                        'volume' => $totals->total_volume,
                        'weight' => $totals->total_weight,
                        'quantity' => $totals->total_quantity,
                    ];
                    $this->orderProcessor->createshipmentbyorder($pickuproute, $orderinfo);
                } else {
                    $pickupinfo['cargo'] = $cargo ?? [];
                    $dropinfo['cargo'] = $cargo ?? [];
                    $pickuproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $pickupinfo);
                    $droproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $dropinfo);
                    if ($pickuproute1 && $droproute1) {
                        $orderinfo = [
                            'id' => $order_id,
                            'order_id' => $booking_id,
                            'shipment_name' => 'BOXES',
                            'customer_phone' => $customer_phone,
                            'customer_email' => $customer_email,
                            'volume' => $totals->total_volume,
                            'weight' => $totals->total_weight,
                            'quantity' => $totals->total_quantity,
                        ];
                        $this->orderProcessor->createshipmentbyorder($pickuproute1, $orderinfo);
                    }
                }
            } else {
                $pickupinfo['cargo'] = $cargo ?? [];
                $dropinfo['cargo'] = $cargo ?? [];
                $pickuproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $pickupinfo);
                $droproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $dropinfo);
                if ($pickuproute1 && $droproute1) {
                    $orderinfo = [
                        'id' => $order_id,
                        'order_id' => $booking_id,
                        'shipment_name' => 'BOXES',
                        'customer_phone' => $customer_phone,
                        'customer_email' => $customer_email,
                        'volume' => $totals->total_volume,
                        'weight' => $totals->total_weight,
                        'quantity' => $totals->total_quantity,
                    ];
                    $this->orderProcessor->createshipmentbyorder($pickuproute1, $orderinfo);
                }
            }


            // Rate management
            $pref_arr = [
                'pickup' => strtoupper($pickup_country),
                'pickup_state' => strtoupper($pickup_state),
                'pickup_city' => strtoupper($pickup_city),
                'pickup_pincode' => $pickup_pincode,
                'drop' => strtoupper($drop_country),
                'drop_state' => strtoupper($drop_state),
                'drop_city' => strtoupper($drop_city),
                'drop_pincode' => $drop_pincode,
                'customer_id' => $customer_code,
                'service' => $service,
                'product' => $product,
                'user_id' => $user_id,
                'org_id' => $org_id,
                'order_type' => $order_type,
                'order_id' => $order_id,
                'customer_row_id' => $customer_id,
            ];


            // $this->addrecodfororderinsertion($pref_arr);
            $this->orderProcessor->addRecordForOrderInsertion($pref_arr);
            // addRecordForOrderInsertion

            DB::commit();

            return response()->json([
                'status' => true,
                'message' => 'Order created successfully.',
                'data' => [
                    'order_id' => $order_id,
                    'booking_id' => $booking_id,
                    'order' => $order
                ]
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating order', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'org_id' => $org_id ?? null,
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to create order: ' . $e->getMessage(),
                'data' => null,
                'errors' => config('app.debug') ? [$e->getMessage()] : [],
            ], 500);
        }
    }


    public function editOrder(Request $request, $id = null)
    {
        $user = Auth::user();
        $data = $orderTypes = [];
        $orderDetails = $shipperDetails = $dropDetails = $pickupDetails = $deliveryArray = $chargeCodes = [];

        // Get currencies
        $data['currencies'] = CountryMaster::getCurrencies();
        $data['currency'] = $request->input('currency', 'USD'); // Default currency if not provided

        // Fetch cargo details using OrderCargodetail model
        $cargo = OrderCargodetail::where(['status' => 1, 'order_id' => $id])->first();
        $data['orderCargoId'] = $cargo ? $cargo->cargo_id : '';

        if ($id) {
            $order = $this->orderProcessor->getOrderToEdit($id);
            if (!$order) {
                return response()->json(['status' => 'error', 'message' => 'Order not found'], 404);
            }

            $incoterm = $order->incoterm;
            $shipmentId = $pickupInst = $deliveryInst = $containerNo = $purchaseOrder = $multipleMarksNumbers = '';

            $references = OrderReference::where('order_id', $id)
                ->whereIn('reference_id', ['DQ', 'PO', 'ORD_DLVINST', 'ORD_PIKINST', 'CTR', 'MARKS_NUMBERS'])
                ->get();

            foreach ($references as $ref) {
                switch ($ref->reference_id) {
                    case 'DQ':
                        $shipmentId = $ref->ref_value;
                        break;
                    case 'ORD_DLVINST':
                        $deliveryInst = $ref->ref_value;
                        break;
                    case 'ORD_PIKINST':
                        $pickupInst = $ref->ref_value;
                        break;
                    case 'CTR':
                        $containerNo = $ref->ref_value;
                        break;
                    case 'PO':
                        $purchaseOrder = $ref->ref_value;
                        break;
                    case 'MARKS_NUMBERS':
                        $multipleMarksNumbers = $ref->ref_value;
                        break;
                }
            }

            $pickupCustId = $order->pickup_custid;
            $status = $order->status;
            $tripId = $order->trip_id;
            $tripSts = $order->trip_sts;

            if ($status == 3) {
                return response()->json(['status' => 'error', 'message' => 'Forbidden access'], 403);
            }

            $orderStatus = 'PENDING';
            if ($tripId != 0 && $tripSts == 0) {
                $orderStatus = 'ACTIVE';
            }
            if ($tripId != 0 && $tripSts == 1) {
                $orderStatus = 'CLOSED';
            }

            $chkDate = Carbon::parse('2020-07-01 00:00:00');
            $created_at = Carbon::parse($order->created_at);
            $earlyPickup = $order->pickup_datetime;
            $earlyDelivery = $order->delivery_datetime;
            $latePickup = $order->pickup_endtime;
            $lateDelivery = $order->drop_endtime;
            $docsReceivedDatetime = $order->docs_received_datetime;
            $docsSentDatetime = $order->docs_sent_datetime;
            $timezone = $request->input('timezone', 'UTC');

            if ($created_at->gt($chkDate)) {
                if ($earlyPickup && $earlyPickup != '0000-00-00 00:00:00') {
                    $earlyPickup = $this->orderProcessor->getDateTimeByTimezone($timezone, $earlyPickup, 'UTC')['datetime'];
                }
                if ($earlyDelivery && $earlyDelivery != '0000-00-00 00:00:00') {
                    $earlyDelivery = $this->orderProcessor->getDateTimeByTimezone($timezone, $earlyDelivery, 'UTC')['datetime'];
                }
                if ($latePickup && $latePickup != '0000-00-00 00:00:00') {
                    $latePickup = $this->orderProcessor->getDateTimeByTimezone($timezone, $latePickup, 'UTC')['datetime'];
                }
                if ($lateDelivery && $lateDelivery != '0000-00-00 00:00:00') {
                    $lateDelivery = $this->orderProcessor->getDateTimeByTimezone($timezone, $lateDelivery, 'UTC')['datetime'];
                }
            }

            if ($docsSentDatetime && $docsSentDatetime != '0000-00-00 00:00:00') {
                $docsSentDatetime = $this->orderProcessor->getDateTimeByTimezone($timezone, $docsSentDatetime, 'UTC')['datetime'];
            }
            if ($docsReceivedDatetime && $docsReceivedDatetime != '0000-00-00 00:00:00') {
                $docsReceivedDatetime = $this->orderProcessor->getDateTimeByTimezone($timezone, $docsReceivedDatetime, 'UTC')['datetime'];
            }

            $orderDetails = [
                'id' => $order->id,
                'orderId' => $order->order_id,
                'shipmentId' => $shipmentId,
                'earlyPickup' => $earlyPickup,
                'earlyDelivery' => $earlyDelivery,
                'latePickup' => $latePickup,
                'lateDelivery' => $lateDelivery,
                'product' => $order->product,
                'service' => $order->service,
                'deliveryTerm' => $order->delivery_term,
                'incoterm' => $order->incoterm,
                'deliveryNote' => $order->delivery_note,
                'purchaseOrder' => $purchaseOrder,
                'multipleMarksNumbers' => $multipleMarksNumbers,
                'notifyParty' => $order->notify_party,
                'goodsValue' => $order->goods_value,
                'currency' => $order->currency,
                'laneReference' => $order->lane_reference,
                'distance' => $order->distance,
                'customsRequired' => $order->customs_required,
                'highCargoValue' => $order->high_cargo_value,
                'valoranceInsurance' => $order->valorance_insurance,
                'temperatureControl' => $order->temperature_control,
                'orgId' => $order->org_id,
                'beValue' => $order->be_value,
                'created_at' => $order->created_at,
                'orderType' => $order->order_type,
                'costCenter' => $order->cost_center_id,
                'transportMode' => $order->transport_mode,
                'shipmentType' => $order->shipment_type,
                'region' => $order->region,
                'pickupInst' => $pickupInst,
                'deliveryInst' => $deliveryInst,
                'containerNo' => $containerNo,
                'docsReceivedDatetime' => $docsReceivedDatetime,
                'docsSentDatetime' => $docsSentDatetime,
                'externalOrderId' => $order->external_order_id ?? '',
                'thirdPartyPost' => $order->third_party_post ?? '',
                'orderStatus' => $orderStatus,
            ];

            if ($incoterm) {
                $deliveryArray = $this->orderProcessor->getDeliveryTermsByIncoterm($incoterm);
            }

            $pickupId = $order->customer_id;
            $vendorId = $order->vendor_id;
            $userId = $user->user_id ?? $request->input('user_id', 0);
            $pickupLocation = [
                'country' => $order->pickup_country,
                'zipcode' => $order->pickup_pincode,
                'user_id' => $userId,
                'city' => $order->pickup_city,
            ];
            $deliveryLocation = [
                'country' => $order->delivery_country,
                'zipcode' => $order->delivery_pincode,
                'user_id' => $userId,
                'city' => $order->delivery_city,
            ];
            $info = ['order_id' => $id, 'product' => $order->product];

            // Assuming these methods are implemented elsewhere
            $data['rates'] = $this->orderProcessor->getCustomerProfileDetailsById($pickupId, $order->service, $pickupLocation, $deliveryLocation, $info);
            $data['vendorRates'] = $this->rateManagement->getvendorprofiledetailsbyid($vendorId, $order->service, $info);

            $orgId = $user->org_id ??  $request->input('org_id', $order->org_id);
            $custId = $user->cust_id ??  $request->input('cust_id', 0);

            if ($custId) {
                $orderTypes = OrderType::where(['org_id' => $orgId, 'customer_id' => $pickupId, 'status' => 1])
                    ->groupBy('type_name', 'id')
                    ->select('id', 'type_name')
                    ->get()
                    ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                    ->toArray();
            } else {
                $orderTypes = OrderType::where(['org_id' => $orgId, 'status' => 1])
                    ->groupBy('id', 'type_name')
                    ->select('id', 'type_name')
                    ->get()
                    ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                    ->toArray();
            }

            if (empty($orderTypes) && $created_at->lt(Carbon::parse('2021-03-19 00:00:00'))) {
                $orderTypes = OrderType::where(['org_id' => $orgId, 'status' => 1])
                    ->groupBy('type_name', 'id')
                    ->select('id', 'type_name')
                    ->get()
                    ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                    ->toArray();
            }

            $pickupDetails = $this->partyManagementService->getPickupDetails($pickupId);
            // $pickupDetails = $this->getPickupDetails($pickupId)['data'];

            $dropId = $order->drop_custid;
            $partyDetails = $this->partyManagementService->getOrderPartyDetails($id);

            foreach ($partyDetails as $rr) {
                $partyType = $this->partyManagementService->getPartyTypeNameById($rr->party_type);
                if ($partyType) {
                    if ($rr->name == $order->delivery || $partyType->type_name == 'Consignee') {
                        $dropDetails = [
                            'id' => $rr->id,
                            'name' => $rr->name,
                            'phone' => $rr->mobile,
                            'email' => $rr->email,
                            'fax' => $rr->fax,
                            'partyId' => $rr->code,
                        ];
                    }
                    if ($rr->name == $order->pickup || $partyType->type_name == 'Shipper') {
                        $shipperDetails = [
                            'id' => $rr->id,
                            'name' => $rr->name,
                            'phone' => $rr->mobile,
                            'email' => $rr->email,
                            'fax' => $rr->fax,
                            'partyId' => $rr->code,
                        ];
                    }
                }
            }

            $shipperDetails['name'] = $order->pickup;
            $shipperDetails['street'] = $order->pickup_address1;
            $shipperDetails['state'] = $order->pickup_address2;
            $shipperDetails['city'] = $order->pickup_city;
            $shipperDetails['country'] = $order->pickup_country;
            $shipperDetails['pincode'] = $order->pickup_pincode;

            $dropDetails['name'] = $order->delivery;
            $dropDetails['street'] = $order->delivery_address1;
            $dropDetails['state'] = $order->delivery_address2;
            $dropDetails['city'] = $order->delivery_city;
            $dropDetails['country'] = $order->delivery_country;
            $dropDetails['pincode'] = $order->delivery_pincode;

            $roles = $this->partyManagementService->getPartyTypes($orgId);

            $chargeCodes = ChargeCode::where('status', 1)
                ->select('id', 'charge_code')
                ->get()
                ->map(fn($res) => ['chargeId' => $res->id, 'chargeCode' => $res->charge_code])
                ->toArray();

            $vasIds = VasMaster::where(['status' => 1, 'org_id' => $orgId])
                ->select('id', 'vas_id', 'vas_name')
                ->get()
                ->map(fn($res) => ['vasRowId' => $res->id, 'vasId' => $res->vas_id . '-' . $res->vas_name])
                ->toArray();
            $transportMode = new TransportMode();
            $transport = $transportMode->getTransportMode(
                $request->input('order_date', $created_at),
                $request->input('less_date', '2021-03-19 00:00:00'),
                $orgId,
                $request->input('be_value', $order->be_value)
            );

            $data['stoppageCodes'] = StoppageMaster::where('status', 1)
                ->select('id', 'code')
                ->get()
                ->map(fn($res) => ['id' => $res->id, 'code' => $res->code])
                ->toArray();

            $data['resolutionCodes'] = ResolutionMaster::where('status', 1)
                ->select('id', 'name')
                ->get()
                ->map(fn($res) => ['id' => $res->id, 'name' => $res->name])
                ->toArray();

            $data['vatCategory'] = VatCategory::where(['org_id' => $orgId, 'status' => 1])
                ->select('id', 'description', 'vat_category', 'vat_percentage')
                ->get()
                ->map(fn($res) => [
                    'id' => $res->id,
                    'val' => $res->id . '_' . $res->vat_category,
                    'desc' => $res->description . ' (' . $res->vat_category . '-' . $res->vat_percentage . ')',
                ])
                ->toArray();

            $data['spotOnData'] = OrderReference::where(['order_id' => $id, 'reference_id' => 'BN'])
                ->select('ref_value', 'updated_at')
                ->get()
                ->toArray();
            $data['spotOnLrExist'] = count($data['spotOnData']) > 0 ? 1 : 0;

            $arrCostCenter = [];
            // Example for specific org_id
            // if ($orgId == 'NZPG') {
            //     $arrCostCenter = CostCenter::where(['org_id' => $orgId, 'status' => 1])
            //         ->groupBy('type_name')
            //         ->select('id', 'type_name')
            //         ->get()
            //         ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
            //         ->toArray();
            // }

            $data['transport'] = $transport;
            $data['orderDetails'] = $orderDetails;
            $data['pickupDetails'] = $pickupDetails;
            $data['dropDetails'] = $dropDetails;
            $data['shipperDetails'] = $shipperDetails;
            $data['orderTypes'] = $orderTypes;
            $data['costCenter'] = $arrCostCenter;
            $data['deliveryArray'] = $deliveryArray;
            $data['chargeCodes'] = $chargeCodes;
            $data['roles'] = $roles;
            $data['vasIds'] = $vasIds;
            $data['marksNumbersColumn'] = [];

            return response()->json([
                'status' => 'success',
                'message' => 'Order details retrieved successfully',
                'data' => $data
            ], 200);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Invalid order ID',
            'data' => []
        ], 400);
    }

    public function getShipperListID(Request $request)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get search parameter
            $search = $request->input('search', '');

            // Build query using service
            $query = $this->partyManagementService->getShipperListQuery($orgId, $search);

            // Add pagination (same style as OrderTypeController)
            $perPage = (int) ($request->input('per_page', 15));
            $page = max(1, (int) ($request->input('page', 1)));

            $shippers = $query->orderBy('id')->paginate($perPage, ['*'], 'page', $page);
            $total = $shippers->total();

            // Map the results (same style as OrderTypeController)
            $mappedShippers = $shippers->map(function ($shipper) {
                return [
                    'id' => $shipper->id,
                    'name' => $shipper->name,
                    'code' => $shipper->code,
                    'mobile' => $shipper->mobile,
                    'email' => $shipper->email,
                    'city' => $shipper->city,
                    'country' => $shipper->country,
                    'street' => $shipper->street,
                    'orgId' => $shipper->org_id,
                    'beValue' => $shipper->be_value
                ];
            });

            return response()->json([
                'status' => true,
                'message' => 'Shipper list retrieved successfully.',
                'data' => [
                    'page' => $shippers->currentPage(),
                    'perPage' => $shippers->perPage(),
                    'total' => $total,
                    'records' => $mappedShippers,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetShipperListID Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to fetch shipper list.',
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getShipperDetailsByID(Request $request)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            $userId = $user->user_id ?? $user->id ?? 0;
            $code = $request->input('id');

            $parties = $this->partyManagementService->getShipperDetailsByID($code, $orgId, $userId);

            return response()->json([
                'status' => 'true',
                'message' => 'Shipper details retrieved successfully',
                'data' => $parties
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetShipperDetailsByID Error: ' . $e->getMessage(), [
                'code' => $request->input('id'),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve shipper details: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function viewPartyList(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            $userId = $user->user_id ?? 0;

            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $type = $request->input('type', '0');
            $partyType = $request->input('party', '');
            $customerId = $request->input('customer_id', '0');
            $subCusts = $user->sub_cust ?? [];

            // Get pagination parameters
            $perPage = (int) ($request->input('per_page', 15));
            $page = max(1, (int) ($request->input('page', 1)));

            $parties = $this->partyManagementService->viewPartyList($userId, $orgId, $type, $partyType, $customerId, $subCusts);

            return response()->json([
                'status' => 'true',
                'message' => 'Party list retrieved successfully',
                'data' => [
                    'page' => $page,
                    'perPage' => $perPage,
                    'total' => count($parties),
                    'records' => $parties,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('ViewPartyList Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to fetch party list.',
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getConsigneeDetailsListByID(Request $request)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            $userId = $user->user_id ?? $user->id ?? 0;
            $code = $request->input('id');

            $parties = $this->partyManagementService->getConsigneeDetailsByID($code, $orgId, $userId);

            return response()->json([
                'status' => 'true',
                'message' => 'Consignee details retrieved successfully',
                'data' => $parties
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetConsigneeDetailsListByID Error: ' . $e->getMessage(), [
                'code' => $request->input('id'),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve consignee details: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function update(StoreOrderRequest $request, $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $org_id = $user->default_org_id;

            if (empty($org_id)) {
                return response()->json([
                    'status' => false,
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            $user_id = $user->id;
            $be_value = $user->be_value ?? 1;
            $curtz = $user->usr_tzone['timezone'] ?? 'UTC';
            $cdate = Carbon::now()->format('Y-m-d H:i:s');

            // Get order ID from route parameter
            $orderId = $id ?: $request->input('order_id', '0');
            if ($orderId === '0') {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid order ID',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            // Validation is automatically handled by UpdateOrderRequest
            $data = $request->validated();

            // Extract validated data with defaults
            $product = $data['product'] ?? '';
            $service = $data['service'] ?? '';
            $shipper_id = $data['shipper_id'] ?? '';
            $delivery_terms = $data['delivery_terms'] ?? '';
            $incoterm = $data['incoterm'] ?? '';
            $shipment_id = $data['delivery_note'] ?? 'SX' . time();
            $container_no = $data['container_num'] ?? '';
            $porder = $data['purchase_order'] ?? '';
            $notify_party = $data['notify_party'] ?? '';
            $currency = $data['currency'] ?? '';
            $goods_value = $data['goods_value'] ?? 0.00;
            $external_order_id = $data['external_order_id'] ?? '';
            $shipment_type = $data['shipment_type'] ?? 0;
            $region = (int)($data['region'] ?? 0);
            $p_latitude = $data['p_latitude'] ?? '';
            $p_longitude = $data['p_longitude'] ?? '';
            $d_latitude = $data['d_latitude'] ?? '';
            $d_longitude = $data['d_longitude'] ?? '';
            $party_row_id = $data['order_party_row_id'] ?? '0';
            $order_inv_row_id = $data['order_inv_row_id'] ?? '0';
            $order_cargo_id = $data['order_cargo_id'] ?? '';
            $pickup = $data['order_pickup_id'] ?? '0';
            $delivery = $data['consignee_id'] ?? '';
            $early_pickup = $data['estimated_early_pickup'] ?? '';
            $late_pickup = $data['estimated_late_pickup'] ?? '';
            $early_delivery = $data['estimated_early_delivery'] ?? '';
            $late_delivery = $data['estimated_late_delivery'] ?? '';
            $modeof_trasnport = $data['mode_of_transport'] ?? 'LTL';
            $order_type = $data['order_type'] ?? '';
            $cost_center = $data['cost_center'] ?? null;
            $rev_row_id = $data['rev_row_id'] ?? '';
            $ordcost_row_id = $data['ordcost_row_id'] ?? '';
            $customer_code = $data['customer_id'] ?? '';
            $driver_pickup_instructions = $data['driver_pickup_instructions'] ?? '';
            $driver_delivery_instructions = $data['driver_delivery_instructions'] ?? '';
            $multiple_marks_numbers = $data['multiple_marks_numbers'] ?? '';
            $multiple_marks_numbers = str_replace(["\r\n", "\r", "\n"], ", ", $multiple_marks_numbers);
            $docs_sent_datetime = $data['docs_sent_datetime'] ?? '';
            $docs_received_datetime = $data['docs_received_datetime'] ?? '';
            $third_party_post = $data['third_party_post'] ?? [];
            $third_party_post_str = !empty($third_party_post) ? implode(',', $third_party_post) : '';

            // Extract new General Info fields
            $cust_do = $data['cust_do'] ?? '';
            $custref_po = $data['custref_po'] ?? '';
            $original_document_sent = $data['original_document_sent'] ?? '';
            $original_document_received = $data['original_document_received'] ?? '';
            
            // Convert empty datetime strings to null for database compatibility
            $original_document_sent = !empty($original_document_sent) ? $original_document_sent : null;
            $original_document_received = !empty($original_document_received) ? $original_document_received : null;
            
            $load_board_options = $data['load_board_options'] ?? [];

            // Extract new Shipper fields
            $shipper_name = $data['shipper_name'] ?? '';
            $shipper_street = $data['shipper_street'] ?? '';
            $shipper_city = $data['shipper_city'] ?? '';
            $shipper_province = $data['shipper_province'] ?? '';
            $shipper_country = $data['shipper_country'] ?? '';
            $shipper_zipcode = $data['shipper_zipcode'] ?? '';
            $shipper_phone = $data['shipper_phone'] ?? '';
            $shipper_fax = $data['shipper_fax'] ?? '';
            $shipper_email = $data['shipper_email'] ?? '';

            // Extract new Consignee fields
            $consignee_name = $data['consignee_name'] ?? '';
            $consignee_street = $data['consignee_street'] ?? '';
            $consignee_city = $data['consignee_city'] ?? '';
            $consignee_province = $data['consignee_province'] ?? '';
            $consignee_country = $data['consignee_country'] ?? '';
            $consignee_zipcode = $data['consignee_zipcode'] ?? '';
            $consignee_phone = $data['consignee_phone'] ?? '';
            $consignee_fax = $data['consignee_fax'] ?? '';
            $consignee_email = $data['consignee_email'] ?? '';

            // Handle cost center lookup - convert string code to integer ID
            $cost_center_id = null;
            if (!empty($cost_center)) {
                if (is_numeric($cost_center)) {
                    // If it's already a numeric ID, use it directly
                    $cost_center_id = (int)$cost_center;
                } else {
                    // If it's a string code, look up the ID
                    $costCenterObj = CostCenter::where('type_name', $cost_center)
                        ->where('org_id', $org_id)
                        ->where('status', 1)
                        ->first();
                    $cost_center_id = $costCenterObj ? $costCenterObj->id : null;
                }
            }

            // Get booking ID
            $order = Order::where('id', $orderId)->select('order_id')->first();
            if ($order) {
                $bookingId = $order->order_id;
            }

            // Resolve party IDs - handle both integer IDs and string codes
            try {
                $delivery_party_id = $this->partyManagementService->resolvePartyId($delivery, $org_id, 'Consignee');
                $shipper_party_id = $this->partyManagementService->resolvePartyId($shipper_id, $org_id, 'Shipper');

                // Update the original variables with resolved IDs
                $delivery = $delivery_party_id;
                $shipper_id = $shipper_party_id;
            } catch (\Exception $e) {
                $field = strpos($e->getMessage(), 'Consignee') !== false ? 'consignee_id' : 'shipper_id';
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage(),
                    'data' => null,
                    'errors' => [$field => 'Invalid party code'],
                ], 422);
            }

            // Process references
            $referenceUpdates = [];
            if (!empty($shipment_id)) {
                $referenceUpdates['DQ'] = $shipment_id;
            }
            if (!empty($driver_pickup_instructions)) {
                $referenceUpdates['ORD_PIKINST'] = $driver_pickup_instructions;
            }
            if (!empty($driver_delivery_instructions)) {
                $referenceUpdates['ORD_DLVINST'] = $driver_delivery_instructions;
            }
            if (!empty($multiple_marks_numbers)) {
                $referenceUpdates['MARKS_NUMBERS'] = $multiple_marks_numbers;
            }
            if (!empty($porder)) {
                $referenceUpdates['PO'] = $porder;
            }
            if (!empty($container_no)) {
                $referenceUpdates['CTR'] = $container_no;
            }

            // Batch update references
            if (!empty($referenceUpdates)) {
                $this->orderProcessor->updateOrderReferencesBatch($orderId, $referenceUpdates);
            }

            // Process pickup and delivery times
            $pickupTimes = $this->orderProcessor->processOrderDatetimes($early_pickup, $late_pickup);
            $ePickup = $this->orderProcessor->getDateTimeByTimezone('UTC', $pickupTimes['early'], $curtz)['datetime'];
            $lPickup = $this->orderProcessor->getDateTimeByTimezone('UTC', $pickupTimes['late'], $curtz)['datetime'];
            $deliveryTimes = $this->orderProcessor->processOrderDatetimes($early_delivery, $late_delivery);
            $eDelivery = $this->orderProcessor->getDateTimeByTimezone('UTC', $deliveryTimes['early'], $curtz)['datetime'];
            $lDelivery = $this->orderProcessor->getDateTimeByTimezone('UTC', $deliveryTimes['late'], $curtz)['datetime'];

            // Process document timestamps with timezone conversion
            if (!empty($docs_sent_datetime) && $docs_sent_datetime !== '0000-00-00 00:00:00') {
                $docs_sent_datetime = $this->orderProcessor->getDateTimeByTimezone('UTC', $docs_sent_datetime, $curtz)['datetime'];
            } else {
                $docs_sent_datetime = null;
            }
            if (!empty($docs_received_datetime) && $docs_received_datetime !== '0000-00-00 00:00:00') {
                $docs_received_datetime = $this->orderProcessor->getDateTimeByTimezone('UTC', $docs_received_datetime, $curtz)['datetime'];
            } else {
                $docs_received_datetime = null;
            }
            
            // Ensure original document fields are null if empty
            if (empty($original_document_sent)) {
                $original_document_sent = null;
            }
            if (empty($original_document_received)) {
                $original_document_received = null;
            }

            // Get party details
            $drop_id = $pickup_custid = 0;
            $pickup_name = $pickup_country = $pickup_street = $pickup_pincode = $pickup_city = $pickup_state = '';
            $drop_name = $drop_country = $drop_street = $drop_pincode = $drop_city = $drop_state = '';
            $pickup_latitude = $pickup_longitude = $drop_latitude = $drop_longitude = $pickup_address = $drop_address = '';

            // Get party details using resolved IDs
            if ($delivery_party_id) {
                $drop_details = $this->partyManagementService->getPartyDetailsOptimized($delivery_party_id);
                if ($drop_details) {
                    $drop_id = $drop_details->customeridentifier ?? $drop_details->code;
                    $drop_name = $drop_details->name;
                    $drop_state = $drop_details->state;
                    $drop_country = $drop_details->country;
                    $drop_street = $drop_details->street;
                    $drop_pincode = $drop_details->pincode;
                    $drop_city = $drop_details->city;
                    $drop_latitude = $drop_details->latitude;
                    $drop_longitude = $drop_details->longitude;
                    $drop_mobile = $drop_details->mobile;
                }
            }

            if ($shipper_party_id) {
                $shipper_details = $this->partyManagementService->getPartyDetailsOptimized($shipper_party_id);
                if ($shipper_details) {
                    $pickup_custid = $shipper_details->customeridentifier ?? $shipper_details->code;
                    $pickup_name = $shipper_details->name;
                    $pickup_state = $shipper_details->state;
                    $pickup_country = $shipper_details->country;
                    $pickup_street = $shipper_details->street;
                    $pickup_pincode = $shipper_details->pincode;
                    $pickup_city = $shipper_details->city;
                    $pickup_latitude = $shipper_details->latitude;
                    $pickup_longitude = $shipper_details->longitude;
                    $pickup_mobile = $shipper_details->mobile;
                }
            }

            // Create order party addresses
            $shipperadd_id = $this->partyManagementService->createOrderPartyAddress(
                $orderId,
                $be_value,
                $user_id,
                $cdate,
                $shipper_id,
                $pickup_city,
                $pickup_street,
                $pickup_state,
                $pickup_address,
                $pickup_pincode,
                $pickup_country
            );

            $dropadd_id = $this->partyManagementService->createOrderPartyAddress(
                $orderId,
                $be_value,
                $user_id,
                $cdate,
                $delivery,
                $drop_city,
                $drop_street,
                $drop_state,
                $drop_address,
                $drop_pincode,
                $drop_country
            );

            // Handle shipper party creation/update if shipper fields are provided
            if (!empty($shipper_name) || !empty($shipper_street) || !empty($shipper_city)) {
                $shipperData = [
                    'name' => $shipper_name ?: 'Shipper',
                    'code' => $shipper_id !== '0' ? $shipper_id : '0',
                    'mobile' => $shipper_phone ?: '0000000000',
                    'email' => $shipper_email ?: '<EMAIL>',
                    'city' => $shipper_city ?: 'Unknown',
                    'country' => $shipper_country ?: 'Unknown',
                    'street' => $shipper_street,
                    'state' => $shipper_province,
                    'pincode' => $shipper_zipcode,
                    'fax' => $shipper_fax,
                    'address' => implode(',', array_filter([
                        $shipper_street,
                        $shipper_city,
                        $shipper_province
                    ]))
                ];

                $shipperResult = $this->partyManagementService->saveShipper($shipperData, $user_id, $org_id, $be_value);
                if ($shipperResult['status'] === 'success' && isset($shipperResult['data']['id'])) {
                    $shipper_id = $shipperResult['data']['id'];
                }
            }

            // Handle consignee party creation/update if consignee fields are provided
            if (!empty($consignee_name) || !empty($consignee_street) || !empty($consignee_city)) {
                $consigneeData = [
                    'name' => $consignee_name ?: 'Consignee',
                    'code' => $delivery !== '0' ? $delivery : '0',
                    'mobile' => $consignee_phone ?: '0000000000',
                    'email' => $consignee_email ?: '<EMAIL>',
                    'city' => $consignee_city ?: 'Unknown',
                    'country' => $consignee_country ?: 'Unknown',
                    'street' => $consignee_street,
                    'state' => $consignee_province,
                    'pincode' => $consignee_zipcode,
                    'fax' => $consignee_fax,
                    'address' => implode(',', array_filter([
                        $consignee_street,
                        $consignee_city,
                        $consignee_province
                    ]))
                ];

                $consigneeResult = $this->partyManagementService->saveShipper($consigneeData, $user_id, $org_id, $be_value);
                if ($consigneeResult['status'] === 'success' && isset($consigneeResult['data']['id'])) {
                    $delivery = $consigneeResult['data']['id'];
                }
            }

            // Update sub customer parties
            $subCutParties = [
                0 => $shipper_id,
                1 => $delivery,
                2 => '0',
                3 => '0'
            ];
            if (!empty(array_filter($subCutParties))) {
                $this->orderProcessor->subcustpartiesinsert($orderId, $bookingId, $subCutParties, $org_id, $be_value, $user_id, $cdate);
            }

            // Get transport mode
            $transportMode = TransportMode::where('code', $modeof_trasnport)
                ->select('id', 'name')
                ->first();
            $tid = $transportMode ? $transportMode->id : '';
            $tname = $transportMode ? $transportMode->name : '';

            // Prepare shipment data
            $shipArr = [
                'unitspec' => 1,
                'shipid' => $shipment_id,
                'txnid' => $shipment_id,
                'trucktype' => $tname,
                'pickupcnt' => '1',
                'dropcnt' => '1',
                'insertusr' => $pickup_custid,
                'carrier' => '0',
                'insertuserdate' => $cdate,
                'enddate' => Carbon::now()->addDay()->format('Y-m-d H:i:s'),
                'insdate' => $cdate,
                'upddate' => $cdate,
                'reason' => 'SHIPMENT',
                'purpose' => 'SEND INTEGRATION',
                'ship_object' => 'SHIPMENT',
                'logdate' => $cdate,
                'transport_mode' => $modeof_trasnport,
                'domainname' => $be_value,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'product' => $product,
                'freight_term' => '60',
                'freight_termname' => 'Free of Charge',
                'incoterm' => $incoterm,
                'modeoftransport' => $tid
            ];

            // Prepare order data
            $ins = [
                'shipment_id' => 0,
                'product' => $product,
                'pickup_datetime' => $ePickup,
                'delivery_datetime' => $eDelivery,
                'pickup_endtime' => $lPickup,
                'drop_endtime' => $lDelivery,
                'goods_value' => $goods_value,
                'currency' => $currency,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'transport_mode' => $modeof_trasnport,
                'third_party_post' => $third_party_post_str,
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),

                // New General Info fields
                'cust_do' => $cust_do,
                'custref_po' => $custref_po,
                'original_document_sent' => $original_document_sent ?: null,
                'original_document_received' => $original_document_received ?: null,
                'load_board_options' => !empty($load_board_options) ? json_encode($load_board_options) : null,

                // New Shipper fields
                'shipper_name' => $shipper_name,
                'shipper_street' => $shipper_street,
                'shipper_city' => $shipper_city,
                'shipper_province' => $shipper_province,
                'shipper_country' => $shipper_country,
                'shipper_zipcode' => $shipper_zipcode,
                'shipper_phone' => $shipper_phone,
                'shipper_fax' => $shipper_fax,
                'shipper_email' => $shipper_email,

                // New Consignee fields
                'consignee_name' => $consignee_name,
                'consignee_street' => $consignee_street,
                'consignee_city' => $consignee_city,
                'consignee_province' => $consignee_province,
                'consignee_country' => $consignee_country,
                'consignee_zipcode' => $consignee_zipcode,
                'consignee_phone' => $consignee_phone,
                'consignee_fax' => $consignee_fax,
                'consignee_email' => $consignee_email
            ];

            $fieldMappings = [
                'pickup_company' => $pickup_name,
                'pickup_country' => $pickup_country,
                'pickup_address1' => $pickup_street,
                'pickup_city' => $pickup_city,
                'pickup_address2' => $pickup_state,
                'pickup_pincode' => $pickup_pincode,
                'delivery_company' => $drop_name,
                'delivery_country' => $drop_country,
                'delivery_address1' => $drop_street,
                'delivery_address2' => $drop_state,
                'delivery_city' => $drop_city,
                'delivery_pincode' => $drop_pincode,
                'customer_id' => $shipper_id,
                'shipment_type' => $shipment_type,
                'region' => $region
            ];

            foreach ($fieldMappings as $dbField => $value) {
                // Always update the field, even if it's empty (to allow clearing values)
                $ins[$dbField] = $value;
            }

            // Process geolocation
            if (!empty($pickup_pincode)) {
                $pickupCoordinates = $this->orderProcessor->getCoordinatesForAddress([$pickup_street, $pickup_city, $pickup_country, $pickup_pincode]);
                if ($pickupCoordinates) {
                    $ins['plat'] = $pickupCoordinates['lat'];
                    $ins['plng'] = $pickupCoordinates['lng'];
                }
            }

            if (!empty($drop_pincode)) {
                $deliveryCoordinates = $this->orderProcessor->getCoordinatesForAddress([$drop_street, $drop_city, $drop_country, $drop_pincode]);
                if ($deliveryCoordinates) {
                    $ins['dlat'] = $deliveryCoordinates['lat'];
                    $ins['dlng'] = $deliveryCoordinates['lng'];
                }
            }

            // Update order
            $updateResult = Order::where('id', $orderId)->update($ins);

            // Log the update result for debugging
            Log::info('Order Update Result', [
                'order_id' => $orderId,
                'affected_rows' => $updateResult,
                'update_data' => $ins
            ]);

            $this->orderProcessor->insertOrdersRefFileLineIdentifier([
                'pickupCity' => $pickup_city,
                'pickupState' => $pickup_state,
                'pickupCountry' => $pickup_country,
                'dropCity' => $drop_city,
                'dropState' => $drop_state,
                'dropCountry' => $drop_country,
                'orgId' => $org_id,
                'beValue' => $be_value,
                'orderRowId' => $orderId,
                'date' => $cdate
            ]);

            // Update order details
            $detailsIns = [
                'service' => $service,
                'delivery_term' => $delivery_terms,
                'incoterm' => $incoterm,
                'notify_party' => $notify_party,
                'lane_reference' => 'LR',
                'distance' => '0',
                'temperature_control' => '0',
                'valorance_insurance' => '0',
                'high_cargo_value' => '0',
                'customs_required' => '0',
                'order_type' => $order_type,
                'cost_center_id' => $cost_center_id,
                'docs_received_datetime' => $docs_received_datetime ?: null,
                'docs_sent_datetime' => $docs_sent_datetime ?: null
            ];

            $orderDetail = OrderDetail::where('order_row_id', $orderId)->first();
            if ($orderDetail) {
                $detailsUpdateResult = OrderDetail::where('order_row_id', $orderId)->update($detailsIns);
                Log::info('Order Details Update Result', [
                    'order_id' => $orderId,
                    'affected_rows' => $detailsUpdateResult,
                    'update_data' => $detailsIns
                ]);
            } else {
                $detailsIns['created_at'] = $cdate;
                $detailsIns['order_row_id'] = $orderId;
                $detailsIns['order_id'] = $bookingId;
                $createdDetail = OrderDetail::create($detailsIns);
                Log::info('Order Details Created', [
                    'order_id' => $orderId,
                    'created_id' => $createdDetail->id,
                    'create_data' => $detailsIns
                ]);
            }

            // Calculate total weight, volume, and quantity
            $totals = DB::table('order_cargodetails as ocd')
                ->leftJoin('cargo_details as cd', 'cd.id', '=', 'ocd.cargo_id')
                ->where('ocd.order_id', $orderId)
                ->where('ocd.status', '1')
                ->selectRaw("
                ROUND(SUM(CASE
                    WHEN cd.weight_unit IN ('G', 'Gms', 'gms', 'grm') THEN ocd.weight / 1000
                    WHEN cd.weight_unit IN ('Kg', 'kg') THEN ocd.weight
                    WHEN cd.weight_unit IN ('Tons', 'tons') THEN ocd.weight * 1000
                    ELSE 0
                END), 3) as total_weight,
                SUM(ocd.volume) as total_volume,
                SUM(ocd.quantity) as total_quantity
            ")
                ->first();

            $totalWeight = $totals->total_weight ?? 0;
            $totalVolume = $totals->total_volume ?? 0;
            $totalQuantity = $totals->total_quantity ?? 0;

            Order::where('id', $orderId)->update([
                'volume' => $totalVolume,
                'weight' => $totalWeight,
                'quantity' => $totalQuantity
            ]);

            // Update shipment and employee data
            $this->orderProcessor->updateShipmentEmployeeData($orderId, $bookingId, $ePickup, $lPickup, $eDelivery, $lDelivery, $totalWeight);

            // Update additional stop details
            $this->orderProcessor->updateAdditionalStopDetails($orderId, $eDelivery, $lDelivery);

            return response()->json([
                'status' => 'true',
                'message' => 'Order Updated Successfully - ' . $bookingId,
                'data' => ['order_id' => $orderId]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Update Order Error: ' . $e->getMessage(), [
                'order_id' => $id ?? 'unknown',
                'user_id' => $user_id ?? 'unknown',
                'org_id' => $org_id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'An error occurred while updating the order: ' . $e->getMessage(),
                'data' => [],
                'errors' => [
                    'general' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }

    public function saveShipper(Request $request)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID from authenticated user
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            $userId = $user->user_id ?? $user->id ?? 0;
            $beValue = $user->be_value ?? 1;

            $data = [
                'name' => $request->input('shipper_name'),
                'code' => $request->input('shipper_id', '0'),
                'mobile' => $request->input('shipper_phone'),
                'email' => $request->input('shipper_email'),
                'city' => $request->input('shipper_city'),
                'country' => $request->input('shipper_country'),
                'street' => $request->input('shipper_street'),
                'state' => $request->input('shipper_state'),
                'pincode' => $request->input('shipper_zipcode'),
                'fax' => $request->input('shipper_fax'),
                'address' => implode(',', array_filter([
                    $request->input('shipper_street'),
                    $request->input('shipper_city'),
                    $request->input('shipper_state')
                ]))
            ];

            $result = $this->partyManagementService->saveShipper($data, $userId, $orgId, $beValue);

            return response()->json($result, $result['status'] === 'success' ? 200 : 500);
        } catch (\Exception $e) {
            Log::error('SaveShipper Error: ' . $e->getMessage(), [
                'shipper_name' => $request->input('shipper_name'),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to save shipper: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function ajaxListing(Request $request)
    {
        $draw = $request->input('draw', 1);
        $start = $request->input('start', 0);
        $orderId = $request->input('data.order_id');
        $actionType = $request->input('data.actionType', 'View');
        $orgId = $request->input('data.org_id', Auth::user()->org_id ?? 0);

        $indexColumn = $orgId === 'AUKN' ? 'orders.id' : 'cargo_details.id';

        $selectColumns = [
            'cargo_details.id',
            'cargo_details.cargo_type',
            'cargo_details.length_unit',
            'cargo_details.width_unit',
            'cargo_details.height_unit',
            'cargo_details.weight_unit',
            'cargo_details.volume_unit',
            'cargo_details.secondweight_uom',
            'cargo_details.secondvolume_uom',
            'cargo_details.goods_description',
            'cargo_details.stackable',
            'cargo_details.grounded',
            'cargo_details.splittable',
            'cargo_details.dg_goods',
            'order_cargodetails.id as order_cargo_id',
            'order_cargodetails.length',
            'order_cargodetails.width',
            'order_cargodetails.height',
            'order_cargodetails.weight',
            'order_cargodetails.second_weight',
            'order_cargodetails.volume',
            'order_cargodetails.second_volume',
            'order_cargodetails.volumetric_weight',
            'order_cargodetails.volweight_uom',
            'cargo_details.ldm',
            'order_cargodetails.quantity',
            'order_cargodetails.scanned_quantity',
            'order_cargodetails.qr_code',
            'order_cargodetails.reference_order_num',
            'order_cargodetails.marks_numbers'
        ];

        $dataTableSortOrdering = $actionType === 'Edit' ? [
            'cargo_details.id',
            'cargo_details.cargo_type',
            'cargo_details.goods_description',
            'order_cargodetails.marks_numbers',
            'order_cargodetails.quantity',
            'order_cargodetails.scanned_quantity',
            'order_cargodetails.length',
            'order_cargodetails.width',
            'order_cargodetails.height',
            'order_cargodetails.weight',
            'order_cargodetails.second_weight',
            'order_cargodetails.volumetric_weight',
            'order_cargodetails.volume',
            'order_cargodetails.second_volume',
            'cargo_details.ldm',
            'cargo_details.stackable',
            'cargo_details.grounded',
            'cargo_details.splittable',
            'cargo_details.dg_goods',
            'cargo_details.id'
        ] : [
            'cargo_details.cargo_type',
            'cargo_details.goods_description',
            'order_cargodetails.marks_numbers',
            'order_cargodetails.quantity',
            'order_cargodetails.scanned_quantity',
            'order_cargodetails.length',
            'order_cargodetails.width',
            'order_cargodetails.height',
            'order_cargodetails.weight',
            'order_cargodetails.second_weight',
            'order_cargodetails.volumetric_weight',
            'order_cargodetails.volume',
            'order_cargodetails.second_volume',
            'cargo_details.ldm',
            'cargo_details.stackable',
            'cargo_details.grounded',
            'cargo_details.splittable',
            'cargo_details.dg_goods',
            'cargo_details.id'
        ];

        $whereCondition = ['order_cargodetails.order_id' => $orderId, 'order_cargodetails.status' => 1];
        // $groupBy = 'cargo_details.id';
        $groupBy = ['cargo_details.id', 'order_cargodetails.id'];


        $getRecordListing = $this->orderProcessor->datatablesQuery($selectColumns, $dataTableSortOrdering, $whereCondition, $indexColumn, $groupBy, $request);

        $totalRecords = $getRecordListing['recordsTotal'];
        $recordsFiltered = $getRecordListing['recordsFiltered'];
        $recordListing = [];

        if (!empty($getRecordListing['data'])) {
            $totalQty = count($getRecordListing['data']);
            foreach ($getRecordListing['data'] as $i => $res) {
                $stackable = $res->stackable ? 'On' : 'Off';
                $grounded = $res->grounded ? 'On' : 'Off';
                $splittable = $res->splittable ? 'On' : 'Off';
                $dgGoods = $res->dg_goods ? 'Yes' : 'No';
                $scannedQuantity = $res->scanned_quantity ?? 0;
                $secondWeight = $res->second_weight ?? 0;
                $secondVolume = $res->second_volume ?? 0;
                $volumetricWeight = $res->volumetric_weight ?? 0;
                $ldm = $res->ldm ?? 0;
                $qrCode = $res->qr_code;

                // Placeholder for uom_string function (to be implemented in frontend)
                $uomString = fn($value, $unit, $decimals = null) => $value ? number_format($value, $decimals ?? 2) . ' ' . $unit : '0 ' . $unit;

                if ($scannedQuantity == 0) {
                    $label = '<a href="#" onclick="alert(\'There is no Labels Scanned for this cargo..!\')"><span class="icon tru-icon-pdf"></span></a>';
                } else {
                    $label = '<a href="' . route('labels.print', ['order_car_id' => $res->order_cargo_id, 'tot' => $totalQty, 'cargo_row_id' => $i]) . '" target="_blank" title="Print Label"><span class="icon tru-icon-pdf"></span></a>';
                }

                $row = [
                    'cargoType' => $res->cargo_type,
                    'goodsDescription' => $res->goods_description,
                    'marksNumbers' => $res->marks_numbers,
                    'quantity' => $res->quantity,
                    'qrCode' => $qrCode,
                    'scannedQuantity' => $scannedQuantity,
                    'length' => $uomString($res->length, $res->length_unit),
                    'width' => $uomString($res->width, $res->width_unit),
                    'height' => $uomString($res->height, $res->height_unit),
                    'weight' => $uomString($res->weight, $res->weight_unit, 3),
                    'secondWeight' => $uomString($secondWeight, $res->secondweight_uom),
                    'volumetricWeight' => $uomString($volumetricWeight, $res->volweight_uom),
                    'volume' => $uomString($res->volume, $res->volume_unit),
                    'secondVolume' => $uomString($secondVolume, $res->secondvolume_uom),
                    'ldm' => $ldm,
                    'stackable' => $stackable,
                    'grounded' => $grounded,
                    'splittable' => $splittable,
                    'dgGoods' => "<span title='Click To Get Dgoods' onclick='getDangerousGoodsDetails({$res->dg_goods}, {$res->order_cargo_id})' style='cursor:pointer;'>{$dgGoods}</span>",
                    'id' => $res->id
                ];

                if ($actionType === 'Edit') {
                    $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'><a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" .
                        "<li><a data-ordertype='editOrder' id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowcargoEdit(this, {$res->id}, \"{$res->cargo_type}\", \"{$res->goods_description}\", 0, {$res->quantity}, {$res->length}, {$res->width}, {$res->height}, {$res->weight}, {$res->volume}, {$volumetricWeight}, {$res->stackable}, {$res->grounded}, {$res->splittable}, {$res->dg_goods}, \"{$res->length_unit}\", \"{$res->width_unit}\", \"{$res->height_unit}\", \"{$res->weight_unit}\", \"{$res->volume_unit}\", \"{$res->volweight_uom}\", {$ldm}, {$scannedQuantity}, {$secondWeight}, \"{$res->secondweight_uom}\", {$secondVolume}, \"{$res->secondvolume_uom}\", " . ($qrCode ?: 0) . ", {$orderId})'><span class='glyphicon glyphicon-pencil'></span>Edit</a></li>" .
                        "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deleteordercargodetails({$res->id})'><span class='glyphicon glyphicon-trash'></span>Remove</a></li>" .
                        "<li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok'></span>Update</a></li>" .
                        "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='rowAdd(this);'><span class='glyphicon glyphicon-plus'></span>Add Cargo Details</a></li>" .
                        "<li><a id='innerpacking' type='button' class='btn btn-sm btn-default' onclick='getinnercargo(this, {$res->id})'><span class='fa fa-archive'></span>Get Inner Cargos</a></li></ul></ul>";
                    $row = ['action' => $action] + $row;
                }

                $recordListing[] = $row;
            }
        }

        return response()->json([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $recordsFiltered,
            'data' => $recordListing
        ], 200);
    }

    public function saveCargo(Request $request, $id = null, $orderId = null)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Cargo Save API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Cargo Save API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id,
                'cargo_id' => $id,
                'order_id' => $orderId
            ]);

            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            $userId = $user->user_id ?? $request->input('user_id', 0);
            $beValue = $user->be_value ?? $request->input('be_value', 0);
            $cdate = Carbon::now()->format('Y-m-d H:i:s');

            // Validate required fields
            $requiredFields = ['cargo_type', 'goods_desc', 'quantity'];
            foreach ($requiredFields as $field) {
                if (!$request->has($field) || empty($request->input($field))) {
                    Log::warning('Cargo Save API: Missing required field', ['field' => $field]);
                    return response()->json([
                        'status' => 'false',
                        'message' => "Required field '{$field}' is missing or empty.",
                        'data' => null,
                        'errors' => ["{$field} is required"]
                    ], 400);
                }
            }

            // Input sanitization
            $stackable = $request->input('stackable', '0') === '1' ? 1 : 0;
            $grounded = $request->input('grounded', '0') === '1' ? 1 : 0;
            $splittable = $request->input('splittable', '0') === '1' ? 1 : 0;
            $dgGoods = $request->input('dg_goods', '0') === '1' ? 1 : 0;
            $length = is_numeric($request->input('length')) ? (float) $request->input('length') : 0.00;
            $width = is_numeric($request->input('width')) ? (float) $request->input('width') : 0.00;
            $height = is_numeric($request->input('height')) ? (float) $request->input('height') : 0.00;
            $weight = is_numeric($request->input('weight')) ? (float) $request->input('weight') : 0.00;
            $volume = is_numeric($request->input('volume')) ? (float) $request->input('volume') : 0.00;
            $ldm = is_numeric($request->input('ldm')) ? (float) $request->input('ldm') : 0.00;
            $itemId = $request->input('item_num_id', 0);
            $secondWeight = is_numeric($request->input('secondweight')) ? (float) $request->input('secondweight') : 0.00;
            $secondVolume = is_numeric($request->input('secondvolume')) ? (float) $request->input('secondvolume') : 0.00;
            $volumetricWeight = is_numeric($request->input('volumetric_weight')) ? (float) $request->input('volumetric_weight') : 0.00;
            $marksNumbers = $request->input('marks_numbers', '');
            $orderForCargo = $request->input('order_forcargo', '0');

            $cargoData = [
                'cargo_type' => $request->input('cargo_type'),
                'goods_description' => $request->input('goods_desc'),
                'quantity' => $request->input('quantity', 0),
                'length' => $length,
                'length_unit' => $request->input('length_uom'),
                'width' => $width,
                'width_unit' => $request->input('width_uom'),
                'height' => $height,
                'height_unit' => $request->input('height_uom'),
                'weight' => $weight,
                'weight_unit' => $request->input('weight_uom'),
                'volume' => $volume,
                'volume_unit' => $request->input('volume_uom'),
                'volumetric_weight' => $volumetricWeight,
                'volweight_uom' => $request->input('volweight_uom'),
                'stackable' => $stackable,
                'grounded' => $grounded,
                'splittable' => $splittable,
                'dg_goods' => $dgGoods,
                'createdby' => $userId,
                'created_at' => $cdate,
                'ldm' => $ldm,
                'second_weight' => $secondWeight,
                'second_volume' => $secondVolume,
                'secondvolume_uom' => $request->input('secondvolume_uom'),
                'secondweight_uom' => $request->input('secondweight_uom'),
                'marks_numbers' => $marksNumbers
            ];

            $cargoId = 0;
            $innerCargoId = [];

            DB::beginTransaction();

            // Ensure all variables are properly defined for this scope
            $org_id = $user->default_org_id;
            $user_id = $user->id;
            $be_value = $user->be_value ?? 1;

            if (!$id || $id === '0') {
                // Check item
                if (!$itemId) {
                    $item = Item::where('item_number', $request->input('cargo_type'))->select('id')->first();
                    $itemId = $item ? $item->id : 0;
                }
                if ($itemId) {
                    $cargoData['item_id'] = $itemId;
                }
                $cargo = CargoDetail::create($cargoData);
                $cargoId = $cargo->id;
            } else {
                $cargoId = $id;
                $qrCode = '';
                if (!$itemId) {
                    $cargoDetail = CargoDetail::where('id', $cargoId)->select('item_id')->first();
                    $itemId = $cargoDetail ? $cargoDetail->item_id : 0;
                }
                if ($itemId) {
                    $cargoData['item_id'] = $itemId;
                    $item = Item::where('id', $itemId)->select('item_number', 'hsn_code', 'color_code', 'color_code_name', 'size_code', 'size_code_name', 'unit_price')->first();
                    if ($item) {
                        $qrCode = $item->item_number;
                    }
                }
                CargoDetail::where('id', $cargoId)->update($cargoData);

                $orderCargoData = [
                    'length' => $length,
                    'width' => $width,
                    'height' => $height,
                    'weight' => $weight,
                    'volume' => $volume,
                    'quantity' => $request->input('quantity', 0),
                    'quantity_type' => $request->input('cargo_type'),
                    'cargo_content' => $request->input('goods_desc'),
                    'volumetric_weight' => $volumetricWeight,
                    'volweight_uom' => $request->input('volweight_uom'),
                    'ldm' => $ldm,
                    'second_weight' => $secondWeight,
                    'second_volume' => $secondVolume,
                    'marks_numbers' => $marksNumbers
                ];
                if ($qrCode) {
                    $orderCargoData['qr_code'] = $qrCode;
                }
                OrderCargodetail::where('cargo_id', $cargoId)->update($orderCargoData);
            }

            if ($orderForCargo > 0) {
                $qrCode = $hsnCode = $colorCode = $colorCodeName = $sizeCode = $sizeCodeName = '';
                $unitPrice = 0.00;
                if ($itemId) {
                    $item = Item::where('id', $itemId)->select('item_number', 'hsn_code', 'color_code', 'color_code_name', 'size_code', 'size_code_name', 'unit_price')->first();
                    if ($item) {
                        $qrCode = $item->item_number;
                        $hsnCode = $item->hsn_code;
                        $colorCode = $item->color_code;
                        $colorCodeName = $item->color_code_name;
                        $sizeCode = $item->size_code;
                        $sizeCodeName = $item->size_code_name;
                        $unitPrice = $item->unit_price ?? 0.00;
                    }
                }

                $handlingUnit = ShipunitType::where('unit_name', $request->input('cargo_type'))->select('id')->first();
                if (!$handlingUnit) {
                    $handlingUnit = ShipunitType::create([
                        'unit_name' => $request->input('cargo_type'),
                        'description' => $request->input('cargo_type'),
                        'user_id' => $userId,
                        'created_at' => $cdate,
                        'status' => 1,
                        'order_id' => $orderForCargo,
                        'be_value' => $beValue,
                        'unit_code' => '',
                    ]);
                }

                $orderCargo = OrderCargodetail::where(['order_id' => $orderForCargo, 'cargo_id' => $cargoId, 'status' => 1])->first();
                $orderCargoData = [
                    'order_id' => $orderForCargo,
                    'cargo_id' => $cargoId,
                    'status' => 1,
                    'created_at' => $cdate,
                    'length' => $length,
                    'width' => $width,
                    'height' => $height,
                    'weight' => $weight,
                    'volume' => $volume,
                    'quantity' => $request->input('quantity', 0),
                    'quantity_type' => $request->input('cargo_type'),
                    'cargo_content' => $request->input('goods_desc'),
                    'handling_unit' => $handlingUnit->id,
                    'volumetric_weight' => $volumetricWeight,
                    'volweight_uom' => $request->input('volweight_uom'),
                    'ldm' => $ldm,
                    'second_weight' => $secondWeight,
                    'second_volume' => $secondVolume,
                    'qr_code' => $qrCode,
                    'marks_numbers' => $marksNumbers,
                    'user_id' => $userId
                ];

                if (!$orderCargo) {
                    OrderCargodetail::create($orderCargoData);
                } else {
                    OrderCargodetail::where(['order_id' => $orderForCargo, 'cargo_id' => $cargoId])->update($orderCargoData);
                }

                if ($qrCode && $colorCode && $sizeCode) {
                    $innerCargoData = [
                        'cargo_id' => $cargoId,
                        'cargo_type' => $request->input('cargo_type'),
                        'goods_description' => $request->input('goods_desc'),
                        'quantity' => $request->input('quantity', 0),
                        'scanned_quantity' => 0,
                        'length' => $length,
                        'length_unit' => $request->input('length_uom'),
                        'width' => $width,
                        'width_unit' => $request->input('width_uom'),
                        'height' => $height,
                        'height_unit' => $request->input('height_uom'),
                        'weight' => $weight,
                        'weight_unit' => $request->input('weight_uom'),
                        'volume' => $volume,
                        'volume_unit' => $request->input('volume_uom'),
                        'stackable' => 0,
                        'qr_code' => $qrCode,
                        'status' => 1,
                        'createdby' => $userId,
                        'created_at' => $cdate,
                        'updated_at' => $cdate,
                        'color_code' => $colorCode,
                        'color_code_name' => $colorCodeName,
                        'size_code' => $sizeCode,
                        'size_name' => $sizeCodeName,
                        'unit_price' => $unitPrice,
                        'd_hsn_code' => $hsnCode
                    ];

                    $innerCargo = InnerCargo::where(['cargo_id' => $cargoId, 'cargo_type' => $request->input('cargo_type')])->first();
                    if ($innerCargo) {
                        InnerCargo::where(['cargo_id' => $cargoId, 'cargo_type' => $request->input('cargo_type')])->update($innerCargoData);
                    } else {
                        $innerCargo = InnerCargo::create($innerCargoData);
                        $innerCargoId[] = $innerCargo->id;
                    }
                }
            }

            // Placeholder for carrierweightupdate->updateCarrierWeight($orderId);
            // Implement if needed

            DB::commit();

            return response()->json([
                'status' => 'true',
                'message' => 'Cargo saved successfully',
                'data' => [
                    'cargoId' => $cargoId,
                    'innerCargoId' => $innerCargoId
                ]
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Cargo Save API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'cargo_id' => $id,
                'order_id' => $orderId,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to save cargo: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function viewItemsList(Request $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Items List API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Items List API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id
            ]);

            // Validate organization ID
            $orgId = $user->default_org_id ?? $request->input('org_id', '0');
            if (empty($orgId) || $orgId === '0') {
                Log::warning('Items List API: Invalid organization ID', ['org_id' => $orgId]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            $type = $request->input('ctype', '');
            $popup = $request->input('popup', '');
            $custId = $request->input('cust_id', $user->cust_id ?? '');

            $query = Item::where('status', 1);

            if ($type) {
                $query->where('item_id', 'not like', "%{$type}%");
            }

            $ironMountainIds = ['415', '187', '188', '192', '226', '428', '431', '430', '426', '427', '418', '417', '429', '419', '423', '416', '422', '425', '420', '424', '489', '421'];
            if ($custId && $custId !== '0' && in_array($custId, $ironMountainIds)) {
                $query->whereIn('item_id', ['Docs', 'Non-Docs']);
            }

            $whereCondition = $this->orderProcessor->getPackagesWhereCondition();
            $query->where(function ($q) use ($whereCondition) {
                $q->where('quickbook_status', '0')
                    ->orWhereIn('item_id', $whereCondition);
            });

            // Group by ID, order by ID descending, limit to 1000
            $items = $query->groupBy('id')
                ->orderBy('id', 'desc')
                ->take(1000)
                ->get();

            $result = [];
            foreach ($items as $res) {
                $secondWeight = $res->second_weight ?? 0;
                $secondVolume = $res->second_volume ?? 0;

                $result[] = [
                    'itemName' => $res->item_name,
                    'itemId' => $res->item_id,
                    'description' => $res->description,
                    'length' => $res->length . ($res->length_unit ?? ''),
                    'width' => $res->width . ($res->width_unit ?? ''),
                    'height' => $res->height . ($res->height_unit ?? ''),
                    'weight' => $res->weight . ($res->weight_unit ?? ''),
                    'volume' => $res->volume . ($res->volume_unit ?? ''),
                    'volumetricWeight' => $res->volumetric_weight . ($res->volweight_uom ?? ''),
                    'secondWeight' => $secondWeight . ($res->secondweight_uom ?? ''),
                    'secondVolume' => $secondVolume . ($res->secondvolume_uom ?? '')
                ];
            }

            Log::info('Items List API: Successfully retrieved items', [
                'user_id' => $user->id,
                'org_id' => $orgId,
                'items_count' => count($result)
            ]);

            return response()->json([
                'status' => 'true',
                'message' => 'Items retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            Log::error('Items List API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve items: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function getItemDetailsList(Request $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Item Details API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Item Details API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id
            ]);

            // Validate organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId) || $orgId === '0') {
                Log::warning('Item Details API: Invalid organization ID', ['org_id' => $orgId]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            $itemId = $request->input('item_id', '');
            $items = [];

            if (!$itemId) {
                Log::warning('Item Details API: Missing item ID', ['user_id' => $user->id]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Item ID is required',
                    'data' => null,
                    'errors' => ['item_id is required']
                ], 400);
            }

            $records = Item::where('status', 1)
                ->where('id', $itemId)
                ->orderBy('created_at', 'desc')
                ->get();

            if ($records->isEmpty()) {
                Log::warning('Item Details API: No item found', [
                    'item_id' => $itemId,
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'No item found for the provided ID',
                    'data' => null,
                    'errors' => []
                ], 404);
            }

            foreach ($records as $res) {
                $secondWeight = $res->second_weight ?? 0;
                $secondVolume = $res->second_volume ?? 0;

                $items[] = [
                    'itemName' => $res->item_name,
                    'itemId' => $res->item_id,
                    'description' => $res->description,
                    'length' => $res->length,
                    'lengthUom' => ucfirst($res->length_unit ?? ''),
                    'width' => $res->width,
                    'widthUom' => ucfirst($res->width_unit ?? ''),
                    'height' => $res->height,
                    'heightUom' => ucfirst($res->height_unit ?? ''),
                    'weight' => $res->weight,
                    'weightUom' => ucfirst($res->weight_unit ?? ''),
                    'volume' => $res->volume,
                    'volumeUom' => ucfirst($res->volume_unit ?? ''),
                    'volumetricWeight' => $res->volumetric_weight,
                    'volweightUom' => ucfirst($res->volweight_uom ?? ''),
                    'secondVolume' => $secondVolume,
                    'secondWeight' => $secondWeight,
                    'secondweightUom' => ucfirst($res->secondweight_uom ?? ''),
                    'secondvolumeUom' => ucfirst($res->secondvolume_uom ?? ''),
                    'itemNumId' => $res->id,
                    'itemNumber' => $res->item_number,
                    'hsnCode' => $res->hsn_code,
                    'colorCode' => $res->color_code,
                    'colorCodeName' => $res->color_code_name,
                    'sizeCode' => $res->size_code,
                    'sizeCodeName' => $res->size_code_name,
                    'unitPrice' => $res->unit_price
                ];
            }

            Log::info('Item Details API: Successfully retrieved item details', [
                'user_id' => $user->id,
                'org_id' => $orgId,
                'item_id' => $itemId,
                'items_count' => count($items)
            ]);

            return response()->json([
                'status' => 'true',
                'message' => 'Item details retrieved successfully',
                'data' => $items
            ], 200);
        } catch (\Exception $e) {
            Log::error('Item Details API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve item details: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function getInnerCargo(Request $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Inner Cargo List API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Inner Cargo List API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id
            ]);

            // Validate organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                Log::warning('Inner Cargo List API: Invalid organization ID', ['org_id' => $orgId]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            $cargoId = $request->input('cargo_id', '');

            if (!$cargoId || $cargoId === '0') {
                Log::warning('Inner Cargo List API: Missing or invalid cargo ID', [
                    'cargo_id' => $cargoId,
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Cargo ID is required and must not be zero',
                    'data' => null,
                    'errors' => ['cargo_id is required and must not be zero']
                ], 400);
            }

            $records = InnerCargo::where('cargo_id', $cargoId)
                ->where('status', 1)
                ->groupBy('id')
                ->orderBy('id', 'desc')
                ->get();

            if ($records->isEmpty()) {
                Log::warning('Inner Cargo List API: No inner cargo found', [
                    'cargo_id' => $cargoId,
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'No inner cargo found for the provided cargo ID',
                    'data' => null,
                    'errors' => []
                ], 404);
            }

            $uomString = fn($value, $unit) => $value ? number_format($value, $unit === 'cbm' ? 3 : 2) . ($unit ? ' ' . $unit : '') : '0' . ($unit ? ' ' . $unit : '');

            $cargos = [];
            foreach ($records as $inner) {
                $stackable = $inner->stackable ? 'On' : 'Off';
                $refOrderNum = $inner->ref_order_num ?? '';

                $cargos[] = [
                    'innerId' => $inner->id,
                    'mainCargoId' => $cargoId,
                    'refOrderNum' => $refOrderNum,
                    'innerCargo' => $inner->cargo_type,
                    'innerGd' => $inner->goods_description,
                    'innerQuantity' => $inner->quantity,
                    'innerWidth' => $uomString($inner->width, $inner->width_unit),
                    'innerHeight' => $uomString($inner->height, $inner->height_unit),
                    'innerLength' => $uomString($inner->length, $inner->length_unit),
                    'innerWeight' => $uomString($inner->weight, $inner->weight_unit),
                    'innerVolume' => $uomString($inner->volume, $inner->volume_unit),
                    'innerStackable' => $stackable,
                    'action' => ''
                ];
            }

            Log::info('Inner Cargo List API: Successfully retrieved inner cargo', [
                'user_id' => $user->id,
                'org_id' => $orgId,
                'cargo_id' => $cargoId,
                'inner_cargo_count' => count($cargos)
            ]);

            return response()->json([
                'status' => 'true',
                'message' => 'Inner cargo retrieved successfully',
                'data' => $cargos
            ], 200);
        } catch (\Exception $e) {
            Log::error('Inner Cargo List API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve inner cargo: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function addItem(Request $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Add Item API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Add Item API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id
            ]);

            // Validate organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                Log::warning('Add Item API: Invalid organization ID', ['org_id' => $orgId]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'item_id' => 'required|string|max:100',
                'item_name' => 'required|string|max:100',
                'description' => 'nullable|string',
                'length' => 'nullable|numeric|min:0',
                'length_uom' => 'nullable|string|max:20',
                'width' => 'nullable|numeric|min:0',
                'width_uom' => 'nullable|string|max:20',
                'height' => 'nullable|numeric|min:0',
                'height_uom' => 'nullable|string|max:20',
                'weight' => 'nullable|numeric|min:0',
                'weight_uom' => 'nullable|string|max:20',
                'volume' => 'nullable|numeric|min:0',
                'volume_uom' => 'nullable|string|max:20',
                'volumetric_weight' => 'nullable|numeric|min:0',
                'volumetricweight_uom' => 'nullable|string|max:20',
                'second_volume' => 'nullable|numeric|min:0',
                'secondvolume_uom' => 'nullable|string|max:20',
                'second_weight' => 'nullable|numeric|min:0',
                'secondweight_uom' => 'nullable|string|max:20',
                'item_number' => 'nullable|string|max:100',
                'hsn_code' => 'nullable|string|max:50',
                'color_code' => 'nullable|string|max:50',
                'color_code_name' => 'nullable|string|max:100',
                'size_code' => 'nullable|string|max:50',
                'size_code_name' => 'nullable|string|max:100',
                'unit_price' => 'nullable|numeric|min:0',
            ]);

            if ($validator->fails()) {
                Log::warning('Add Item API: Validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null,
                    'errors' => $validator->errors()->all()
                ], 422);
            }

            $data = [
                'item_id' => $request->input('item_id'),
                'item_name' => $request->input('item_name'),
                'description' => $request->input('description', ''),
                'status' => 1,
                'createdby' => $user->id,
                'length' => $request->input('length', 0),
                'length_unit' => $request->input('length_uom', ''),
                'width' => $request->input('width', 0),
                'width_unit' => $request->input('width_uom', ''),
                'height' => $request->input('height', 0),
                'height_unit' => $request->input('height_uom', ''),
                'weight' => $request->input('weight', 0),
                'weight_unit' => $request->input('weight_uom', ''),
                'volume' => $request->input('volume', 0),
                'volume_unit' => $request->input('volume_uom', ''),
                'volumetric_weight' => $request->input('volumetric_weight', 0),
                'volweight_uom' => $request->input('volumetricweight_uom', ''),
                'second_volume' => $request->input('second_volume', 0),
                'secondvolume_uom' => $request->input('secondvolume_uom', 'cbm'),
                'second_weight' => $request->input('second_weight', 0),
                'secondweight_uom' => $request->input('secondweight_uom', 'kg'),
                'item_number' => $request->input('item_number', ''),
                'hsn_code' => $request->input('hsn_code', ''),
                'color_code' => $request->input('color_code', ''),
                'color_code_name' => $request->input('color_code_name', ''),
                'size_code' => $request->input('size_code', ''),
                'size_code_name' => $request->input('size_code_name', ''),
                'unit_price' => $request->input('unit_price', 0.00),
            ];

            // Check for existing item based on item_id and item_name only
            $existingItem = Item::where('item_id', $request->input('item_id'))
                ->where('item_name', $request->input('item_name'))
                ->where('status', 1)
                ->first();

            if ($existingItem) {
                Log::info('Add Item API: Item already exists', [
                    'item_id' => $request->input('item_id'),
                    'item_name' => $request->input('item_name'),
                    'existing_item_id' => $existingItem->id,
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'true',
                    'message' => 'Item already exists',
                    'data' => [
                        'id' => $existingItem->id,
                        'ins' => 'upd'
                    ]
                ], 200);
            }

            // Insert new item
            $item = Item::create($data);

            Log::info('Add Item API: Successfully added new item', [
                'item_id' => $item->id,
                'item_name' => $item->item_name,
                'user_id' => $user->id
            ]);

            return response()->json([
                'status' => 'true',
                'message' => 'Item added successfully',
                'data' => [
                    'id' => $item->id,
                    'ins' => 'ins'
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Add Item API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to add item: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function saveInnerCargo(Request $request, $id = null)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Save Inner Cargo API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Save Inner Cargo API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id,
                'inner_cargo_id' => $id
            ]);

            // Validate organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                Log::warning('Save Inner Cargo API: Invalid organization ID', ['org_id' => $orgId]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'main_cargo_id' => 'required|integer|min:1',
                'cargo_type' => 'required|string|max:255',
                'goods_desc' => 'nullable|string|max:255',
                'quantity' => 'required|integer|min:1',
                'length' => 'nullable|numeric|min:0',
                'length_uom' => 'nullable|string|max:60',
                'width' => 'nullable|numeric|min:0',
                'width_uom' => 'nullable|string|max:60',
                'height' => 'nullable|numeric|min:0',
                'height_uom' => 'nullable|string|max:60',
                'weight' => 'nullable|numeric|min:0',
                'weight_uom' => 'nullable|string|max:60',
                'volume' => 'nullable|numeric|min:0',
                'volume_uom' => 'nullable|string|max:60',
                'stackable' => 'required|in:0,1',
            ]);

            if ($validator->fails()) {
                Log::warning('Save Inner Cargo API: Validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null,
                    'errors' => $validator->errors()->all()
                ], 422);
            }

            $mainCargoId = $request->input('main_cargo_id');
            $data = [
                'cargo_id' => $mainCargoId,
                'cargo_type' => $request->input('cargo_type'),
                'goods_description' => $request->input('goods_desc', ''),
                'quantity' => $request->input('quantity'),
                'length' => $request->input('length', 0.00),
                'length_unit' => $request->input('length_uom', ''),
                'width' => $request->input('width', 0.00),
                'width_unit' => $request->input('width_uom', ''),
                'height' => $request->input('height', 0.00),
                'height_unit' => $request->input('height_uom', ''),
                'weight' => $request->input('weight', 0.00),
                'weight_unit' => $request->input('weight_uom', ''),
                'volume' => $request->input('volume', 0.00),
                'volume_unit' => $request->input('volume_uom', ''),
                'stackable' => $request->input('stackable', 0),
                'org_id' => $orgId,
                'user_id' => $user->id,
                'status' => 1
            ];

            if ($id === null) {
                // Insert new inner cargo
                $data['createdby'] = $user->id;
                $data['created_at'] = now();
                $innerCargo = InnerCargo::create($data);
                $innerCargoId = $innerCargo->id;
                $message = 'Inner cargo added successfully';
                $statusCode = 201;

                Log::info('Save Inner Cargo API: Successfully created new inner cargo', [
                    'user_id' => $user->id,
                    'org_id' => $orgId,
                    'inner_cargo_id' => $innerCargoId,
                    'main_cargo_id' => $mainCargoId
                ]);
            } else {
                // Update existing inner cargo
                $innerCargo = InnerCargo::find($id);
                if (!$innerCargo) {
                    Log::warning('Save Inner Cargo API: Inner cargo not found', [
                        'inner_cargo_id' => $id,
                        'user_id' => $user->id
                    ]);
                    return response()->json([
                        'status' => 'false',
                        'message' => 'Inner cargo not found',
                        'data' => null,
                        'errors' => []
                    ], 404);
                }

                // If main_cargo_id is not provided, use the existing one
                if (!$mainCargoId) {
                    $mainCargoId = $innerCargo->cargo_id;
                    $data['cargo_id'] = $mainCargoId;
                }

                $innerCargo->update($data);
                $innerCargoId = $id;
                $message = 'Inner cargo updated successfully';
                $statusCode = 200;

                Log::info('Save Inner Cargo API: Successfully updated inner cargo', [
                    'user_id' => $user->id,
                    'org_id' => $orgId,
                    'inner_cargo_id' => $innerCargoId,
                    'main_cargo_id' => $mainCargoId
                ]);
            }

            return response()->json([
                'status' => 'true',
                'message' => $message,
                'data' => [
                    'mainCargoId' => $mainCargoId,
                    'innerCargoId' => $innerCargoId
                ]
            ], $statusCode);
        } catch (\Exception $e) {
            Log::error('Save Inner Cargo API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'inner_cargo_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to save inner cargo: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function deleteOrderCargoDetails(Request $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Delete Cargo API: No authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                    'errors' => []
                ], 401);
            }

            // Log request for debugging
            Log::info('Delete Cargo API called', [
                'request_data' => $request->all(),
                'user_id' => $user->id
            ]);

            // Validate organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                Log::warning('Delete Cargo API: Invalid organization ID', ['org_id' => $orgId]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                    'errors' => []
                ], 400);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1',
                'cargo_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                Log::warning('Delete Cargo API: Validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null,
                    'errors' => $validator->errors()->all()
                ], 422);
            }

            $orderId = $request->input('order_id');
            $cargoId = $request->input('cargo_id');

            // Validate order exists and user has access
            $order = \App\Models\Order::where('id', $orderId)
                ->where('org_id', $orgId)
                ->where('status', 1)
                ->first();

            if (!$order) {
                Log::warning('Delete Cargo API: Order not found or access denied', [
                    'order_id' => $orderId,
                    'org_id' => $orgId,
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Order not found or access denied.',
                    'data' => null,
                    'errors' => []
                ], 404);
            }

            // Find the order cargo detail
            $orderCargoDetail = OrderCargodetail::where('order_id', $orderId)
                ->where('cargo_id', $cargoId)
                ->where('status', 1)
                ->first();

            if (!$orderCargoDetail) {
                Log::warning('Delete Cargo API: Order cargo detail not found', [
                    'order_id' => $orderId,
                    'cargo_id' => $cargoId,
                    'user_id' => $user->id
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Order cargo detail not found.',
                    'data' => null,
                    'errors' => []
                ], 404);
            }

            // Soft delete the order cargo detail
            $orderCargoDetail->update(['status' => 0]);

            // Get the next active cargo for the order
            $activeCargo = OrderCargodetail::where('order_id', $orderId)
                ->where('status', 1)
                ->first();
            $orderCargoId = $activeCargo ? $activeCargo->cargo_id : '';

            Log::info('Delete Cargo API: Successfully deleted', [
                'order_id' => $orderId,
                'cargo_id' => $cargoId,
                'user_id' => $user->id,
                'remaining_cargo_id' => $orderCargoId
            ]);

            return response()->json([
                'status' => 'true',
                'message' => 'Order cargo detail deleted successfully',
                'data' => [
                    'orderCargoId' => $orderCargoId
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Delete Cargo API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to delete order cargo detail: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function getOrderInvolvedParties(Request $request, $id = null)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get order ID from path parameter or request body
            $orderId = $id ?? $request->input('order_id') ?? $request->input('id');

            // Log for debugging
            Log::info('GetOrderInvolvedParties Debug', [
                'path_id' => $id,
                'request_order_id' => $request->input('order_id'),
                'request_id' => $request->input('id'),
                'final_order_id' => $orderId,
                'request_data' => $request->all(),
                'user_id' => $user->id ?? 'unknown'
            ]);

            if (empty($orderId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order ID is required. Please provide order ID in the URL path or request body.',
                    'data' => null,
                ], 422);
            }

            // Validate order ID
            if (!is_numeric($orderId) || (int)$orderId < 1) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid order ID. Order ID must be a positive number.',
                    'data' => null,
                ], 422);
            }

            $orderId = (int) $orderId;
            $customerId = $request->input('cust_id', $user->cust_id ?? 0);

            // Get shift_id from orders
            $order = Order::where('id', $orderId)
                ->where('status', '>', 0)
                ->select('shift_id')
                ->first();

            if (!$order) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order not found or inactive.',
                    'data' => null,
                ], 404);
            }

            $shiftId = $order->shift_id ?? 0;

            // Get involved parties
            $parties = $this->partyManagementService->getInvolvedPartiesForOrder($orderId);

            $result = [];
            foreach ($parties as $res) {
                $partyName = $res->party_name;
                $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'><li><a id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowPartyEdit(this, \"{$res->party_master_id}\", \"{$res->code}\", \"{$res->username}\", \"{$res->street}\", \"{$res->emailid}\", \"{$res->mobile}\", \"{$res->state}\", \"{$res->country}\", \"{$res->zipcode}\", \"{$res->fax}\", \"{$res->city}\", \"{$partyName}\", \"{$orderId}\")'><span class='glyphicon glyphicon-pencil'></span>Edit</a></li><li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deletepartydetailswithorder(\"{$res->party_master_id}\")'><span class='glyphicon glyphicon-trash'></span>Remove</a></li><li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='rowAddParty(this);'><span class='glyphicon glyphicon-plus'></span>Add Parties</a></li></ul></li></ul>";

                // Filter based on customer_id and CARRIER logic
                $includeParty = true;
                if ($customerId) {
                    if (strtoupper($partyName) === 'CARRIER') {
                        $includeParty = false;
                    }
                } else {
                    if (strtoupper($partyName) === 'CARRIER' && $shiftId == 0) {
                        $includeParty = false;
                    }
                }

                if ($includeParty) {
                    $result[] = [
                        'id' => $res->party_master_id,
                        'partyId' => $res->code,
                        'street' => $res->street,
                        'partyType' => $partyName,
                        'name' => $res->username,
                        'username' => $res->username,
                        'email' => $res->emailid,
                        'mobile' => $res->mobile,
                        'zipcode' => $res->zipcode,
                        'customerIdentifier' => $res->code,
                        'partyIdentifier' => $res->partyindetifier,
                        'fax' => $res->fax,
                        'code' => $res->code,
                        'city' => $res->city,
                        'state' => $res->state,
                        'country' => $res->country,
                        'orgId' => $res->org_id,
                        'beValue' => $res->b_value
                    ];
                }
            }

            return response()->json([
                'status' => 'true',
                'message' => 'Involved parties retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetOrderInvolvedParties Error: ' . $e->getMessage(), [
                'order_id' => $request->input('order_id'),
                'user_id' => $user->id ?? 'unknown',
                'org_id' => $orgId ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve involved parties.',
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getPartyDetailsListById(Request $request)
    {
        try {
            // Get id from request (could be in different fields)
            $id = $request->input('id') ?? $request->input('party_id') ?? $request->input('code');

            // Log for debugging
            Log::info('GetPartyDetailsListById Debug', [
                'id' => $id,
                'request_data' => $request->all(),
                'all_inputs' => $request->all()
            ]);

            // Check if id is provided
            if (empty($id)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Party ID is required. Please provide id, party_id, or code parameter.',
                    'data' => null
                ], 422);
            }

            // Convert id to string and validate format
            $id = (string) $id;
            if (strlen($id) > 100) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid party ID. ID must be maximum 100 characters.',
                    'data' => null
                ], 422);
            }

            // Validate type if provided
            $type = $request->input('type');
            if ($type && !in_array($type, ['inv', 'consignee'])) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid type parameter. Must be either "inv" or "consignee".',
                    'data' => null
                ], 422);
            }

            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $userId = $user->user_id ?? 0;


            $parties = $this->partyManagementService->getPartyDetailsListById($id, $orgId, $userId);

            // Check if party was found
            if (empty($parties['partyDetails'])) {
                return response()->json([
                    'status' => false,
                    'message' => 'Party not found with the provided ID.',
                    'data' => null
                ], 404);
            }

            return response()->json([
                'status' => 'true',
                'message' => 'Party details retrieved successfully',
                'data' => $parties
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetPartyDetailsListById Error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve party details.',
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function addInvolvedPartyForOrder(Request $request, $order_id = null)
    {
        try {
            // Validate order_id parameter
            if (empty($order_id)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order ID is required.',
                    'data' => null
                ], 422);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'party_id' => 'required|string|max:100',
                'party_name' => 'required|string|max:100',
                'street' => 'nullable|string|max:255',
                'zipcode' => 'nullable|string|max:20',
                'country' => 'nullable|string|max:100',
                'state' => 'nullable|string|max:100',
                'mobile' => 'nullable|string|max:20',
                'fax' => 'nullable|string|max:50',
                'email' => 'nullable|email|max:100',
                'role' => 'required|string|max:100',
                'city' => 'nullable|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // Authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            // Authorization
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $userId = $user->user_id ?? 0;
            $beValue = $user->be_value ?? 0;

            // Call service method
            $result = $this->partyManagementService->addInvolvedPartyForOrder(
                $order_id,
                $request->input('party_id'),
                $request->input('party_name'),
                $request->input('street', ''),
                $request->input('zipcode', ''),
                $request->input('country', ''),
                $request->input('state', ''),
                $request->input('mobile', ''),
                $request->input('fax', ''),
                $request->input('email', ''),
                $request->input('role'),
                $request->input('city', ''),
                $userId,
                $orgId,
                $beValue
            );

            // Return response based on service result
            if ($result['status'] === 'success') {
                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ], 201);
            } else {
                // Check if it's a validation error (party not found)
                if (strpos($result['message'], 'not found') !== false) {
                    return response()->json([
                        'status' => false,
                        'message' => $result['message'],
                        'data' => null
                    ], 404);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => $result['message'],
                        'data' => null
                    ], 500);
                }
            }
        } catch (\Exception $e) {
            Log::error('AddInvolvedPartyForOrder Error: ' . $e->getMessage(), [
                'order_id' => $order_id,
                'user_id' => $user->id ?? 'unknown',
                'org_id' => $orgId ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to add involved party.',
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function deleteOrderPartyDetails(Request $request)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1',
                'party_id' => 'required|integer|min:1',
                'party_type' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => ['resultCode' => 0]
                ], 422);
            }

            $order_id = $request->input('order_id');
            $party_id = $request->input('party_id');
            $party_type = $request->input('party_type', '');

            $result = $this->partyManagementService->deleteOrderPartyDetails($order_id, $party_id, $party_type, $orgId);

            return response()->json($result, $result['status'] === 'true' ? 200 : 500);
        } catch (\Exception $e) {
            Log::error('DeleteOrderPartyDetails Error: ' . $e->getMessage(), [
                'order_id' => $request->input('order_id'),
                'party_id' => $request->input('party_id'),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to delete order party: ' . $e->getMessage(),
                'data' => ['resultCode' => 0]
            ], 500);
        }
    }

    public function getOtherReferenceDetails(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'order_id' => 'nullable|integer|min:1|exists:orders,id',
                'type' => 'nullable|string|in:popup,other',
                'ref_ids' => 'nullable|array',
                'ref_ids.*' => 'integer|min:1|exists:reference_master,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $order_id = $request->input('order_id');
            $type = $request->input('type', '');
            $ref_ids = $request->input('ref_ids', []);

            $references = [];

            if ($order_id) {
                $query = ReferenceMaster::select([
                    'reference_master.id',
                    'reference_master.name',
                    'reference_master.description',
                    'order_references.order_shortNo',
                    'order_references.id as order_ref_id',
                    'order_references.ref_value as order_value'
                ])
                    ->join('order_references', 'reference_master.name', '=', 'order_references.reference_id')
                    ->where('order_references.order_id', $order_id)
                    ->where('reference_master.status', 1)
                    ->where('order_references.status', 1)
                    ->groupBy('order_references.id', 'reference_master.id')
                    ->orderBy('reference_master.created_at', 'desc');
            } elseif (!empty($ref_ids)) {
                $query = ReferenceMaster::select([
                    'reference_master.id',
                    'reference_master.name',
                    'reference_master.description',
                    'order_references.order_shortNo',
                    'order_references.id as order_ref_id',
                    'order_references.ref_value as order_value'
                ])
                    ->join('order_references', 'reference_master.name', '=', 'order_references.reference_id')
                    ->whereIn('reference_master.id', $ref_ids)
                    ->where('reference_master.status', 1)
                    ->where('order_references.status', 1)
                    ->groupBy('reference_master.id', 'reference_master.id')
                    ->orderBy('reference_master.created_at', 'desc');
            } else {
                return response()->json([
                    'status' => true,
                    'message' => 'No references found',
                    'data' => []
                ], 200);
            }

            $results = $query->get();

            foreach ($results as $res) {
                $res_val = $res->order_value;
                if ($res_val) {
                    try {
                        $parsed_date = Carbon::createFromFormat('Y-m-d H:i:s', $res_val, 'UTC');
                        if ($parsed_date !== false) {
                            $timezone = $user->timezone ?? config('app.timezone', 'UTC');
                            $res_val = $parsed_date->timezone($timezone)->toDateTimeString();
                        }
                    } catch (\Exception $e) {
                        // If parsing fails, keep the original value
                    }
                }

                $references[] = [
                    'id' => $res->name,
                    'name' => $res->description,
                    'value' => $res_val,
                    'orderShortNo' => $res->order_shortNo,
                    'orderRefId' => $res->order_ref_id
                ];
            }

            return response()->json([
                'status' => true,
                'message' => 'Reference details retrieved successfully',
                'data' => $references
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetOtherReferenceDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve reference details: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function triporderintoshipment(TripOrderToShipmentRequest $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            $userId = $user->user_id ?? $request->input('user_id') ?? 0;
            $orgId = $user->org_id ?? $request->input('org_id') ?? 0;
            $beValue = $user->be_value ?? $request->input('be_value') ?? 0;
            $curtz = ($user->usr_tzone && isset($user->usr_tzone['timezone'])) ? $user->usr_tzone['timezone'] : ($request->input('curtz') ?? 'Asia/Singapore');
            
            try {
                $curdt = Carbon::now()->setTimezone($curtz)->format('Y-m-d H:i:s');
            } catch (\Exception $timezoneException) {
                Log::error('TripOrderIntoShipment Timezone Error: ' . $timezoneException->getMessage());
                $curdt = Carbon::now()->format('Y-m-d H:i:s');
            }

            $input = $request->all();
            $input['user_id'] = $userId;
            $input['org_id'] = $orgId;
            $input['be_value'] = $beValue;
            $input['curtz'] = $curtz;
            $input['curdt'] = $curdt;

            $orderIds = explode(',', $input['ordid']);
            Log::error('orderIds::' . json_encode($orderIds));

            $response = 0;
            try {
                if (count($orderIds) > 1) {
                    $response = $this->tripCreateFromOrders->tripcreatemultiorder($input);
                } else {
                    $response = $this->tripCreateFromOrders->eachordercreatetrip($input);
                }

                // Check if trip already exists
                if ($response === -1) {
                    return response()->json([
                        'status' => false,
                        'message' => 'This order already has a trip created. Cannot create duplicate trip.',
                        'data' => null
                    ], 422);
                }

                $this->rateManagement->addrecodfortripinsertion($input);
            } catch (\Exception $serviceException) {
                Log::error('TripOrderIntoShipment Service Error: ' . $serviceException->getMessage());
                Log::error('TripOrderIntoShipment Service Trace: ' . $serviceException->getTraceAsString());
                return response()->json([
                    'status' => false,
                    'message' => 'Service error occurred: ' . $serviceException->getMessage(),
                    'data' => null
                ], 500);
            }

            // Handle response - service methods return different types
            $data = [];
            $vroId = 0;

            if ($response > 0) {
                // Check if response is the actual vroId (from createVehicleRouteOptimizationPlan)
                if (isset($input['veh_route_plan_flag']) && $input['veh_route_plan_flag'] == 1) {
                    $vroId = $response;
                    $data['vroId'] = $vroId;
                } else {
                    // For other cases, try to get vroId from database
                    try {
                        $ordid = trim($input['ordid'] ?? '');
                        if ($ordid !== '') {
                            $orderIds = array_map('trim', explode(',', $ordid));
                            
                            // Use PostgreSQL-compatible query for multiple order IDs
                            $vroResult = DB::table('vro_orders')
                                ->select('id')
                                ->where('status', 1)
                                ->where(function ($query) use ($orderIds) {
                                    foreach ($orderIds as $orderId) {
                                        $query->orWhereRaw('? = ANY(string_to_array(order_ids, \',\'))', [$orderId]);
                                    }
                                })
                                ->first();

                            if ($vroResult) {
                                $vroId = $vroResult->id;
                                $data['vroId'] = $vroId;
                            }
                        } else {
                            Log::warning('TripOrderIntoShipment: ordid is empty when fetching VRO ID');
                        }
                    } catch (\Exception $dbException) {
                        Log::error('TripOrderIntoShipment DB Query Error: ' . $dbException->getMessage());
                        Log::error('TripOrderIntoShipment DB Query Trace: ' . $dbException->getTraceAsString());
                        // Continue without the DB query result
                    }
                }
                
                // Check for route optimization redirect
                if (
                    !empty($input['no_of_vehicles']) &&
                    !empty($input['dimension_type']) &&
                    isset($input['veh_route_plan_flag']) &&
                    $input['veh_route_plan_flag'] == 1 &&
                    $input['trip_type'] === 'multi' &&
                    $vroId > 0
                ) {
                    $data['redirectUrl'] = url('vehiclerouteoptimize/newTrip/' . $vroId);
                    return response()->json([
                        'status' => true,
                        'message' => 'Trip created successfully, redirect URL provided for vehicle route optimization',
                        'data' => $data
                    ], 200);
                }
                
                return response()->json([
                    'status' => true,
                    'message' => 'Trip created successfully',
                    'data' => $data
                ], 200);
            } elseif ($response == 2) {
                return response()->json([
                    'status' => false,
                    'message' => 'Selected orders are not in the same route (e.g., Ref. ID (ROT))',
                    'data' => null
                ], 422);
            }

            return response()->json([
                'status' => false,
                'message' => 'Failed to create trip',
                'data' => null
            ], 500);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('TripOrderIntoShipment Validation Error: ' . $e->getMessage());
            $errors = $e->errors();
            $firstError = reset($errors);
            $errorMessage = is_array($firstError) ? $firstError[0] : $firstError;
            return response()->json([
                'status' => false,
                'message' => 'Validation failed: ' . $errorMessage,
                'data' => null
            ], 422);
        } catch (\Exception $e) {
            Log::error('TripOrderIntoShipment Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to create trip: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function destroy(Request $request, int $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            // Validate ID parameter
            if (empty($id) || $id <= 0) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid order ID.',
                    'data' => null,
                    'errors' => ['id' => 'Order ID is required and must be a positive integer.'],
                ], 422);
            }

            // Check if order exists and belongs to user's organization
            $order = Order::where('id', $id)
                ->where('org_id', $orgId)
                ->first();

            if (!$order) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Order not found or access denied.',
                    'data' => null,
                    'errors' => [],
                ], 404);
            }

            // Check if order can be deleted (not assigned to active trip)
            if ($order->trip_id && $order->trip_id > 0) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Cannot delete order that is assigned to a trip.',
                    'data' => null,
                    'errors' => [],
                ], 422);
            }

            // Log deletion attempt
            Log::info('Order deletion attempt', [
                'order_id' => $id,
                'order_number' => $order->order_id,
                'user_id' => $user->id,
                'org_id' => $orgId
            ]);

            // Delete order using OrderProcessor
            $deleteOrder = $this->orderProcessor->deleteOrderById($id);
            if ($deleteOrder) {
                // Delete related order references
                $this->orderProcessor->deleteForOrder($id, ['DQ']);

                // Log successful deletion
                Log::info('Order deleted successfully', [
                    'order_id' => $id,
                    'order_number' => $order->order_id,
                    'user_id' => $user->id,
                    'org_id' => $orgId
                ]);

                return response()->json([
                    'status' => 'true',
                    'message' => 'Order deleted successfully.',
                    'data' => [
                        'orderId' => $id,
                        'orderNumber' => $order->order_id,
                        'deletedAt' => \Carbon\Carbon::now()->format('Y-m-d H:i:s')
                    ]
                ], 200);
            }

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to delete order.',
                'data' => null,
                'errors' => [],
            ], 500);
        } catch (\Exception $e) {
            Log::error('Order deletion error', [
                'order_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'An error occurred while deleting the order: ' . $e->getMessage(),
                'data' => null,
                'errors' => config('app.debug') ? [$e->getMessage()] : [],
            ], 500);
        }
    }


    public function getOrderStatus(Request $request)
    {
        try {
            $orderStatuses = $this->orderProcessor->getOrderStatus();

            return response()->json([
                'status' => 'true',
                'message' => 'Order statuses retrieved successfully',
                'data' => [
                    'orderStatusData' => $orderStatuses
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Order Status API Error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve order statuses: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }



    public function getShipmentTypes(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();

            $shipmentTypes = $this->orderProcessor->getShipmentTypes();

            return response()->json([
                'status' => 'true',
                'message' => 'Shipment types retrieved successfully',
                'data' => [
                    'shipmentTypeData' => $shipmentTypes
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetShipmentTypes Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve shipment types: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }


    public function show(Request $request, $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            // Validate ID parameter - can be either numeric ID or string order_id
            if (empty($id)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Order ID is required.',
                    'data' => null,
                    'errors' => ['id' => 'Order ID is required.'],
                ], 422);
            }

            // Get user details with proper fallbacks
            $userId = $user->user_id ?? $user->id ?? 0;
            $currency = $user->timezone['currency'] ?? 'USD';

            // Get order data using OrderProcessor
            $data = $this->orderProcessor->viewOrder($id, $request->query('urlString'), $orgId, $userId, $currency);

            // Check if order was found
            if (empty($data['generalInfo'])) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Order not found or access denied.',
                    'data' => null,
                    'errors' => [],
                ], 404);
            }

            // Cargo details are available via existing cargo APIs:
            // GET /api/orders/cargodetails-listing?data[order_id]={order_id}

            return response()->json([
                'status' => 'true',
                'message' => 'Order details retrieved successfully.',
                'data' => $data,
            ], 200);
        } catch (\Exception $e) {
            Log::error('ViewOrder Error: ' . $e->getMessage(), [
                'order_id' => $id,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve order details: ' . $e->getMessage(),
                'data' => null,
                'errors' => config('app.debug') ? [$e->getMessage()] : [],
            ], 500);
        }
    }

    public function copyOrder(CopyOrderRequest $request, $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Validation is now handled by CopyOrderRequest FormRequest
            Log::info('CopyOrder - Starting processing', [
                'order_id' => $id,
                'org_id' => $orgId,
                'user_id' => $user->user_id ?? 0,
                'user_default_org_id' => $user->default_org_id
            ]);

            // Get user details
            $userId = $user->user_id ?? 0;
            $currency = $user->timezone['currency'] ?? 'USD';

            // Copy order using OrderProcessor
            $data = $this->orderProcessor->copyOrder($id, $orgId, $userId, $currency);

            // Check if order was found and copied successfully
            if (empty($data)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order not found or access denied.',
                    'data' => null,
                ], 404);
            }

            return response()->json([
                'status' => 'true',
                'message' => 'Order details for copying retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error('CopyOrder Error: ' . $e->getMessage(), [
                'order_id' => $id,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to copy order details: ' . $e->getMessage(),
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function reverseOrder(ReverseOrderRequest $request, $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            // Get organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Validation is now handled by ReverseOrderRequest FormRequest
            Log::info('ReverseOrder - Starting processing', [
                'order_id' => $id,
                'org_id' => $orgId,
                'user_id' => $user->user_id ?? 0,
                'user_default_org_id' => $user->default_org_id
            ]);

            // Get user details
            $userId = $user->user_id ?? 0;
            $currency = $user->timezone['currency'] ?? 'USD';

            // Reverse order using OrderProcessor
            $data = $this->orderProcessor->reverseOrder($id, $orgId, $userId, $currency);

            // Check if order was found and reversed successfully
            if (empty($data)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order not found or access denied.',
                    'data' => null,
                ], 404);
            }

            return response()->json([
                'status' => 'true',
                'message' => 'Order details for reversing retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error('ReverseOrder Error: ' . $e->getMessage(), [
                'order_id' => $id,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to reverse order details: ' . $e->getMessage(),
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function cancelOrder(Request $request, $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::error('CancelOrder API - No user found');
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null,
                ], 401);
            }

            Log::info('CancelOrder API - User found', [
                'user_id' => $user->user_id,
                'org_id' => $user->default_org_id ?? 'null'
            ]);

            // Get organization ID
            $orgId = $user->default_org_id;
            if (empty($orgId)) {
                Log::error('CancelOrder API - No org ID', ['user_id' => $user->user_id]);
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Validate ID parameter
            if (empty($id) || !is_numeric($id)) {
                Log::error('CancelOrder API - Invalid ID', ['id' => $id]);
                return response()->json([
                    'status' => false,
                    'message' => 'Valid order ID is required.',
                    'data' => null,
                ], 422);
            }

            // Get user details
            $userId = $user->user_id ?? 0;
            $timezone = 'UTC';
            $countryHours = '+00:00';
            $beValue = $user->be_value ?? 0;

            Log::info('CancelOrder API - Calling service', [
                'order_id' => $id,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $beValue
            ]);

            // Test database connection first
            try {
                $testOrder = \App\Models\Order::where('id', $id)->first();
                Log::info('CancelOrder API - Database test', [
                    'order_exists' => $testOrder ? 'yes' : 'no',
                    'order_status' => $testOrder ? $testOrder->status : 'N/A'
                ]);
            } catch (\Exception $dbError) {
                Log::error('CancelOrder API - Database error', [
                    'error' => $dbError->getMessage(),
                    'trace' => $dbError->getTraceAsString()
                ]);
                throw $dbError;
            }

            $this->orderProcessor->cancelOrder($id, $orgId, $userId, $timezone, $countryHours, $beValue);

            Log::info('CancelOrder API - Success');
            return response()->json([
                'status' => 'true',
                'message' => 'Order cancelled successfully',
                'data' => []
            ], 200);
        } catch (\Exception $e) {
            Log::error('CancelOrder API Error: ' . $e->getMessage(), [
                'order_id' => $id,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to cancel order: ' . $e->getMessage(),
                'data' => null,
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function checkTrip(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $orderId = $request->input('order_id');
            $userId = $user->id ?? 0;
            $orgId = $user->default_org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $result = $this->orderProcessor->checkTrip($orderId, $userId, $orgId, $beValue);

            return response()->json([
                'status' => true,
                'message' => 'Trip check completed successfully',
                'data' => $result
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('CheckTrip Validation Error: ' . $e->getMessage());
            $errors = $e->errors();
            $firstError = reset($errors);
            $errorMessage = is_array($firstError) ? $firstError[0] : $firstError;
            return response()->json([
                'status' => false,
                'message' => 'Validation failed: ' . $errorMessage,
                'data' => null
            ], 422);
        } catch (\Exception $e) {
            Log::error('CheckTrip Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Error checking trip: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function allocationRulePriority(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            $query = CarrierAllocationRule::where(['status' => 1]);
            $allocationRules = $query->orderBy('priority_type')->get(['id', 'priority_type as AllocationRulePriorityName']);
            return response()->json([
                'status' => 'true',
                'message' => 'Allocation rule priorities retrieved successfully',
                'data' => [
                    'allocationRules' => $allocationRules,
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('AllocationRulePriority Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve allocation rule priorities: ' . $e->getMessage(),
                'data' => null,
                'errors' => []
            ], 500);
        }
    }

    public function getDriverId(Request $request, $id)
    {
        try {
            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $userId = $user->user_id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $contactNumber = $this->orderProcessor->getDriverId($id);

            return response()->json([
                'status' => true,
                'message' => 'Driver contact number retrieved successfully',
                'data' => [
                    'contactNumber' => $contactNumber
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetDriverId Validation Error: ' . $e->getMessage());
            $errors = $e->errors();
            $firstError = reset($errors);
            $errorMessage = is_array($firstError) ? $firstError[0] : $firstError;
            return response()->json([
                'status' => false,
                'message' => 'Validation failed: ' . $errorMessage,
                'data' => null
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetDriverId Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Error retrieving driver contact number: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function addReferenceDetails(Request $request)
    {
        try {
            $validated = $request->validate([
                'reference_id' => 'required|string',
                'reference_name' => 'required|string',
                'reference_value' => 'required|string',
                'ref_row_id' => 'nullable|string',
                'order_id' => 'nullable|string',
                'order_ref_id' => 'nullable|string',
            ]);

            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            $referenceId = $request->input('reference_id');
            $referenceName = $request->input('reference_name');
            $referenceValue = $request->input('reference_value');
            $refRowId = $request->input('ref_row_id', '');
            $orderId = $request->input('order_id', '0');
            $orderRefId = $request->input('order_ref_id', '0');

            $userId = $user->user_id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;
            $timezone = $user->timezone['timezone'] ?? 'UTC';

            $result = $this->orderProcessor->addReferenceDetails($referenceId, $referenceName, $referenceValue, $refRowId, $orderId, $orgId, $beValue, $timezone);

            return response()->json([
                'status' => true,
                'message' => 'Reference details added successfully',
                'data' => $result
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('AddReferenceDetails Validation Error: ' . $e->getMessage());
            $errors = $e->errors();
            $firstError = reset($errors);
            $errorMessage = is_array($firstError) ? $firstError[0] : $firstError;
            return response()->json([
                'status' => false,
                'message' => 'Validation failed: ' . $errorMessage,
                'data' => null
            ], 422);
        } catch (\Exception $e) {
            Log::error('AddReferenceDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to add reference details: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function deleteOrderReferenceDetails(Request $request)
    {
        try {
            $validated = $request->validate([
                'order_id' => 'required|string',
                'ref_id' => 'required|string',
            ]);

            $user = Auth::guard('api')->user();
            if (!$user || !is_object($user)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access.',
                    'data' => null
                ], 401);
            }

            $orderId = $request->input('order_id');
            $refId = $request->input('ref_id');

            $success = $this->orderProcessor->deleteOrderReferenceDetails($orderId, $refId);

            return response()->json([
                'status' => true,
                'message' => $success ? 'Order reference deleted successfully' : 'No matching order reference found',
                'data' => [
                    'success' => $success,
                    'order_id' => $orderId,
                    'ref_id' => $refId
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('DeleteOrderReferenceDetails Validation Error: ' . $e->getMessage());
            $errors = $e->errors();
            $firstError = reset($errors);
            $errorMessage = is_array($firstError) ? $firstError[0] : $firstError;
            return response()->json([
                'status' => false,
                'message' => 'Validation failed: ' . $errorMessage,
                'data' => null
            ], 422);
        } catch (\Exception $e) {
            Log::error('DeleteOrderReferenceDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete order reference: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * Download orders template file directly
     * 
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function downloadOrdersTemplate()
    {
        try {
            // Simple authentication check - following project pattern
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            // Fixed template file path - using existing document storage path
            $templatePath = 'assets/poduploads/orders_template.xlsx';
            $fullPath = base_path('public/' . $templatePath);

            // Log request for debugging
            Log::info('Orders Template Download API called', [
                'template_path' => $templatePath,
                'user_id' => $user->user_id ?? $user->id ?? 0,
                'org_id' => $orgId
            ]);

            // Check if template file exists
            if (!file_exists($fullPath)) {
                Log::warning('Orders template file not found', [
                    'template_path' => $templatePath,
                    'full_path' => $fullPath,
                    'user_id' => $user->user_id ?? $user->id ?? 0
                ]);
                return response()->json([
                    'status' => 'false',
                    'message' => 'Orders template file not found.',
                    'data' => null,
                    'errors' => ['template' => 'Orders template file not found.'],
                ], 404);
            }

            // Log successful download
            Log::info('Orders template download successful', [
                'template_path' => $templatePath,
                'user_id' => $user->user_id ?? $user->id ?? 0
            ]);

            // Return success response instead of file download
            return response()->json([
                'status' => 'true',
                'message' => 'Orders template file is available.',
                'data' => [
                    'template_path' => $templatePath,
                    'file_name' => 'orders_template.xlsx',
                    'download_url' => url($templatePath)
                ],
                'errors' => []
            ], 200);

        } catch (\Exception $e) {
            Log::error('Orders Template Download Error: ' . $e->getMessage(), [
                'user_id' => $user->user_id ?? $user->id ?? 0,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to download orders template: ' . $e->getMessage(),
                'data' => null,
                'errors' => [],
            ], 500);
        }
    }

    /**
     * Upload orders template file
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadOrdersTemplate(Request $request)
    {
        try {
            // Log the request for debugging
            Log::info('Orders Template Upload API called - METHOD REACHED', [
                'request_data' => $request->all(),
                'files' => $request->allFiles(),
                'method' => $request->method(),
                'url' => $request->url(),
                'route_name' => $request->route() ? $request->route()->getName() : 'unknown'
            ]);

            // Simple authentication check - following project pattern
            $user = Auth::guard('api')->user();
            if (!$user) {
                Log::warning('Upload failed - no authenticated user');
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            $orgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'User organization ID not set.',
                    'data' => null,
                    'errors' => [],
                ], 400);
            }

            // Handle file upload - check for both field names
            $file = $request->file('template_file') ?? $request->file('template_file_');
            
            if (!$file) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Template file is required.',
                    'data' => null,
                    'errors' => ['template_file' => 'Template file is required.'],
                ], 422);
            }

            // Validate file upload
            $validated = $request->validate([
                'template_file' => 'nullable|file|mimes:xlsx,xls|max:10240', // Max 10MB
                'template_file_' => 'nullable|file|mimes:xlsx,xls|max:10240', // Max 10MB
            ]);
            $allowedExtensions = ['xlsx', 'xls'];
            $fileExtension = strtolower($file->getClientOriginalExtension());

            if (!in_array($fileExtension, $allowedExtensions)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid file type. Only Excel files (.xlsx, .xls) are allowed.',
                    'data' => null,
                    'errors' => ['template_file' => 'Invalid file type.'],
                ], 422);
            }

            // Generate unique filename
            $fileName = 'orders_template_' . time() . '.' . $fileExtension;
            
            // Use Laravel's storage system for better cross-platform compatibility
            $filePath = $file->storeAs('public/assets/poduploads', $fileName);
            $relativePath = str_replace('public/', '', $filePath);

            // Log successful upload
            Log::info('Orders template upload successful', [
                'file_name' => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'file_path' => $filePath,
                'relative_path' => $relativePath,
                'user_id' => $user->user_id ?? $user->id ?? 0,
                'org_id' => $orgId
            ]);

            // Return success response
            return response()->json([
                'status' => 'true',
                'message' => 'Orders template uploaded successfully.',
                'data' => [
                    'file_name' => $fileName,
                    'original_name' => $file->getClientOriginalName(),
                    'file_size' => $file->getSize(),
                    'file_path' => $relativePath,
                    'download_url' => url($relativePath)
                ],
                'errors' => []
            ], 200);

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Orders Template Upload Validation Error: ' . $e->getMessage());
            $errors = $e->errors();
            $firstError = reset($errors);
            $errorMessage = is_array($firstError) ? $firstError[0] : $firstError;
            return response()->json([
                'status' => 'false',
                'message' => 'Validation failed: ' . $errorMessage,
                'data' => null,
                'errors' => $errors,
            ], 422);
        } catch (\Exception $e) {
            Log::error('Orders Template Upload Error: ' . $e->getMessage(), [
                'user_id' => $user->user_id ?? $user->id ?? 0,
                'org_id' => $orgId ?? 0,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to upload orders template: ' . $e->getMessage(),
                'data' => null,
                'errors' => [],
            ], 500);
        }
    }
}
