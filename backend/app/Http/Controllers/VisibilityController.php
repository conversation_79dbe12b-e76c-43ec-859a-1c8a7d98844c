<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\GetShipmentsRequest;
use App\Http\Requests\AssignVehicleRequest;
use App\Http\Requests\GetHistoryAndDocumentsRequest;
use App\Http\Requests\BillingDetailsRequest;
use App\Http\Requests\CalculateEmissionsRequest;
use App\Http\Requests\GenerateTWBRequest;
use App\Http\Requests\ShowNearbyVehiclesRequest;
use App\Http\Requests\SecureLinkRequest;
use App\Http\Requests\TripEtnshipmentRequest;
use App\Http\Requests\RollbackTripShipmentDeleteRequest;
use App\Models\Trip;
use App\Models\TrucksData;
use App\Models\ShiftVehicle;
use App\Models\AssignedDriver;
use App\Models\TruckDriver;
use App\Models\VehiclesDriver;
use App\Models\StopStatus;
use App\Models\StatusMaster;
use App\Models\Order;
use App\Models\Driver;
use Carbon\Carbon;
use Picqer\Barcode\BarcodeGeneratorPNG;
use DateTime;
use Exception;

class VisibilityController extends Controller
{
    public function __construct()
    {
        
    }

    /**
     * Display a listing of Shipments.
     */
    public function shipments(GetShipmentsRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Sanitize and validate pagination parameters
            $page = (int) $request->query('page', 1);
            $perPage = (int) $request->query('per_page', 10);
            $offset = ($page - 1) * $perPage;

            $total = DB::table('shipment as s')
                        ->leftJoin('shft_veh as sv', 's.id', '=', 'sv.shft_id')
                        ->leftJoin('trucks_data as t', 'sv.vehicle_id', '=', 't.id')
                        ->leftJoin('assigned_drivers as ad', 't.id', '=', 'ad.vehicle_id')
                        ->leftJoin('truck_drivers as td', 'ad.driver_id', '=', 'td.id')
                        ->leftJoin('sx_users as u_customer', 's.customer_id', '=', 'u_customer.id')
                        ->leftJoin('sx_users as u_vendor', 's.vendor_id', '=', 'u_vendor.id')
                        ->select(
                            's.id',
                            's.splace',
                            's.slat',
                            's.slng',
                            's.eplace',
                            's.elat',
                            's.elng',
                            's.weight',
                            's.volume',
                            's.units',
                            's.startdate',
                            's.enddate',
                            'u_customer.employee_name as customer_name',
                            'u_vendor.employee_name as carrier_name',
                            'td.name as driver_name',
                            't.truck_number as vehicle_number'
                        )
                        ->where('s.org_id', $org_id)
                        ->where(function($query) use ($id, $org_id) {
                            // Direct ownership OR linked through orders
                            $query->where('s.user_id', $id)
                                  // OR: Orders linked via trips (o.trip_id → trips.shift_id = s.id)
                                  ->orWhereExists(function($subQuery) use ($id, $org_id) {
                                      $subQuery->select(DB::raw(1))
                                          ->from('orders as o')
                                          ->join('trips as t', 'o.trip_id', '=', 't.id')
                                          ->whereColumn('t.shift_id', 's.id')
                                          ->where('o.user_id', $id)
                                          ->where('o.org_id', $org_id)
                                          ->where('o.trip_id', '!=', 0)
                                          ->where('o.status', '!=', 0)
                                          ->where('t.status', '!=', 0);
                                  })
                                  // OR: Orders linked via shift_id (o.shift_id = s.id)
                                  ->orWhereExists(function($subQuery) use ($id, $org_id) {
                                      $subQuery->select(DB::raw(1))
                                          ->from('orders as o')
                                          ->whereColumn('o.shift_id', 's.id')
                                          ->where('o.user_id', $id)
                                          ->where('o.org_id', $org_id)
                                          ->where('o.shift_id', '!=', 0)
                                          ->where('o.status', '!=', 0);
                                  });
                        })
                        ->distinct()
                        ->count();

            $results = DB::table('shipment as s')
                ->leftJoin('shft_veh as sv', 's.id', '=', 'sv.shft_id')
                ->leftJoin('trucks_data as t', 'sv.vehicle_id', '=', 't.id')
                ->leftJoin('assigned_drivers as ad', 't.id', '=', 'ad.vehicle_id')
                ->leftJoin('truck_drivers as td', 'ad.driver_id', '=', 'td.id')
                ->leftJoin('sx_users as u_customer', 's.customer_id', '=', 'u_customer.id')
                ->leftJoin('sx_users as u_vendor', 's.vendor_id', '=', 'u_vendor.id')
                ->select(
                    's.id',
                    's.splace',
                    's.slat',
                    's.slng',
                    's.eplace',
                    's.elat',
                    's.elng',
                    's.weight',
                    's.volume',
                    's.units',
                    's.startdate',
                    's.enddate',
                    'u_customer.employee_name as customer_name',
                    'u_vendor.employee_name as carrier_name',
                    'td.name as driver_name',
                    't.truck_number as vehicle_number'
                )
                ->where('s.org_id', $org_id)
                ->where(function($query) use ($id, $org_id) {
                    // Direct ownership OR linked through orders
                    $query->where('s.user_id', $id)
                          // OR: Orders linked via trips (o.trip_id → trips.shift_id = s.id)
                          ->orWhereExists(function($subQuery) use ($id, $org_id) {
                              $subQuery->select(DB::raw(1))
                                  ->from('orders as o')
                                  ->join('trips as t', 'o.trip_id', '=', 't.id')
                                  ->whereColumn('t.shift_id', 's.id')
                                  ->where('o.user_id', $id)
                                  ->where('o.org_id', $org_id)
                                  ->where('o.trip_id', '!=', 0)
                                  ->where('o.status', '!=', 0)
                                  ->where('t.status', '!=', 0);
                          })
                          // OR: Orders linked via shift_id (o.shift_id = s.id)
                          ->orWhereExists(function($subQuery) use ($id, $org_id) {
                              $subQuery->select(DB::raw(1))
                                  ->from('orders as o')
                                  ->whereColumn('o.shift_id', 's.id')
                                  ->where('o.user_id', $id)
                                  ->where('o.org_id', $org_id)
                                  ->where('o.shift_id', '!=', 0)
                                  ->where('o.status', '!=', 0);
                          });
                })
                ->distinct()
                ->orderBy('s.id', 'asc')
                ->offset($offset)
                ->limit($perPage)
                ->get();

            $shipmentsData = $results->toArray();
            $shiftDetails = [];
            foreach ($shipmentsData as $eachShipment) {
                // Convert stdClass to array
                $shipmentData = (array) $eachShipment;
                $shiftDetails[$shipmentData['id']] = $shipmentData;
                $shiftDetails[$shipmentData['id']]['stops'] = [];
            }

            $result = [
                        'success' => true,
                        'message' => 'shipments retrieved successfully.',
                        'data' => $shiftDetails,
                        'count' => count($shiftDetails),
                        'pagination' => [
                            'total' => $total,
                            'per_page' => $perPage,
                            'current_page' => $page,
                            'last_page' => (int) ceil($total / $perPage),
                        ]
                    ];

            $employeeData = $stopIds = $stopsData = [];
            if (!empty($shipmentsData)) {
                $shiftIds = array_column($shipmentsData, 'id');
                $getStopIds = DB::table('shiporder_stops')
                    ->select('id', 'shipment_id', 'plat', 'plng', 'stoptype', 'stopstatus')
                    ->whereIn('shipment_id', $shiftIds)
                    ->where('status', 1)
                    ->orderBy('id', 'asc')
                    ->get();

                foreach ($getStopIds as $stops) {
                    if ($stops->id > 0) {
                        $stopsData[$stops->id] = (array) $stops;
                        $stopIds[] = $stops->id;
                    }
                }
                if (empty($stopIds)) {
                    $result = [
                                'success' => true,
                                'message' => 'shipments retrieved successfully.',
                                'data' => $shiftDetails,
                                'count' => count($shiftDetails),
                                'pagination' => [
                                    'total' => $total,
                                    'per_page' => $perPage,
                                    'current_page' => $page,
                                    'last_page' => (int) ceil($total / $perPage),
                                ]
                            ];
                    return response()->json($result, 200);
                }
                if(count($shiftDetails) < $perPage) {
                    if($offset > 0) {
                        $offset = $offset - (int)ceil($total / $perPage) - 1; // -1 is added to offset to get the correct result
                    }
                    $perPage = $perPage-count($shiftDetails);
                }

                $stopsTotal = DB::table('shiporder_stop_sequence')
                    ->select('id', 'stop_id', 'drop_stopid')
                    ->whereIn('stop_id', $stopIds)
                    ->where('status', 1)
                    ->union(
                        DB::table('shiporder_stop_sequence')
                            ->select('id', 'stop_id', 'drop_stopid')
                            ->whereIn('drop_stopid', $stopIds)
                            ->where('status', 1)
                    )
                    ->count();

                $getEmployeeIds = DB::table('shiporder_stop_sequence')
                    ->select('id', 'stop_id', 'drop_stopid')
                    ->whereIn('stop_id', $stopIds)
                    ->where('status', 1)
                    ->union(
                        DB::table('shiporder_stop_sequence')
                            ->select('id', 'stop_id', 'drop_stopid')
                            ->whereIn('drop_stopid', $stopIds)
                            ->where('status', 1)
                    )
                    ->offset($offset)
                    ->limit($perPage)
                    ->get();

                // Convert collection to array and convert stdClass objects to arrays
                $employeeDataArray = $getEmployeeIds->toArray();
                $employeeData = array_map(function($item) {
                    return (array) $item;
                }, $employeeDataArray);
                
                $stopsData = $this->arrangeEmployeeStopsData($employeeData, $stopsData, $stopIds);
                $employeeStopDetails = [];
                foreach ($employeeData as $i => $iValue) {
                    $employeeStopDetails[$iValue['stop_id']][] = $employeeData[$i]['id'];
                }
                $stopsData = $this->getEmployeeStopsStatus($employeeStopDetails, $stopIds, $shiftIds, $stopsData);
                foreach ($stopsData as $eachStop) {
                    $shipmentId = $eachStop['shipment_id'];
                    $shiftDetails[$shipmentId]['stops'][] = $eachStop;
                }
                $result = [
                                'success' => true,
                                'message' => 'shipments retrieved successfully.',
                                'data' => $shiftDetails,
                                'count' => count($shiftDetails),
                                'pagination' => [
                                    'total' => $total + $stopsTotal,
                                    'per_page' => $perPage,
                                    'current_page' => $page,
                                    'last_page' => (int) ceil(($total + $stopsTotal) / $perPage),
                                ]
                            ];
            }

            return response()->json($result, 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get shipments data.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function arrangeEmployeeStopsData($employeeData, $stopsData, $stopIds)
    {
        foreach ($employeeData as $i => $iValue) {
            $stopId = $iValue['stop_id'];
            if (in_array($stopId, $stopIds)) {
                $stopsData[$stopId]['employee_id'] = $employeeData[$i]['id'];
            }
            $dropStopId = $iValue['drop_stopid'];
            if (in_array($dropStopId, $stopIds)) {
                $stopsData[$dropStopId]['employee_id'] = $employeeData[$i]['id'];
            }
        }
        return $stopsData;
    }

    public function getEmployeeStopsStatus($employeeStopDetails, $stopIds, $shiftIds, $stopsData)
    {
        foreach ($employeeStopDetails as $stopId => $employeeIds) {
            if (in_array($stopId, $stopIds)) {
                $stopStatus = 0;
                foreach ($employeeIds as $employeeId) {
                    if (isset($stopsData[$stopId]['employee_id']) && $stopsData[$stopId]['employee_id'] == $employeeId) {
                        $stopStatus = 1;
                    }
                }
                $stopsData[$stopId]['stopstatus'] = $stopStatus;
            }
        }
        return $stopsData;
    }

    public function openAssignVehicle(AssignVehicleRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $data = [];
            $data['driver'] = 0;
            $shift_id = $request->input('shift_id');
            $carrier_id = $request->input('carrier_id');
            $data["volume"] = $request->input('volume');
            $data["vehicle_id"] = $request->input('vehicle_id');
            $data["shipment_id"] = $shift_id;
            $data["carrier_id"] = $carrier_id;
        $chklegs = DB::table('shipment')
            ->select('id')
            ->where('shift_leg_id', $shift_id)
            ->limit(1)
            ->get();
        if (count($chklegs) == 0) {
            $schedule_date = DB::table('shipment')->select("*")->where('id', $shift_id)->limit(1)->get();
            $data["schedule_date"] = "";
            if (count($schedule_date) > 0) {
                $data["schedule_date"] = $schedule_date[0]->schedule_date;
            }
            if ($data["schedule_date"] == "") {
                $data["schedule_date"] = date("Y-m-d H:i:s");
            }
            $data["carrier_instructions"] = isset($schedule_date[0]) ? $schedule_date[0]->carrier_instructions : null;
            $data["weight_capacity"] = isset($schedule_date[0]) ? $schedule_date[0]->weight_capacity : null;
            $data["volume_capacity"] = isset($schedule_date[0]) ? $schedule_date[0]->volume_capacity : null;
            $data["additional_conditions"] = isset($schedule_date[0]) ? $schedule_date[0]->additional_conditions : null;
            $data["temperature_regime"] = isset($schedule_date[0]) ? $schedule_date[0]->temperature_regime : null;
            $data["time_for_loading_penality_rate"] = isset($schedule_date[0]) ? $schedule_date[0]->time_for_loading_penality_rate : null;
            $data['vehicle_type'] = isset($schedule_date[0]) ? $schedule_date[0]->vehicle_type : null;
            $category_data = DB::table('shipment_rates')
                ->select('category', 'rate')
                ->where('shift_id', $shift_id)
                ->limit(1)
                ->get();
            $data["category"] = isset($category_data[0]) ? $category_data[0]->category : null;
            $data["rate"] = isset($category_data[0]) ? $category_data[0]->rate : null;
            $getCarrierCode = DB::table('sx_users')
                ->select('employee_name')
                ->where('id', $carrier_id)
                ->limit(1)
                ->get();
            $data["carrier_code"] = isset($getCarrierCode[0]) ? $getCarrierCode[0]->employee_name : null;
            // Try to get driver from available tables
            $driver_found = false;
            
            // First try: shipment_vehicle_histories table
            try {
                $driver_query = DB::table('shipment_vehicle_histories')
                    ->select('driver_id')
                    ->where('shft_id', $shift_id)
                    ->where('status', 1)
                    ->orderBy('id', 'desc')
                    ->limit(1)
                    ->get();
                
                if(count($driver_query) > 0 && $driver_query[0]->driver_id){
                    $data['driver'] = $driver_query[0]->driver_id;
                    $driver_found = true;
                }
            } catch (\Exception $e) {
                // Table doesn't exist, continue to fallback
            }
            
            // Fallback: Use shft_veh with assigned_drivers (if first attempt failed)
            if (!$driver_found) {
                try {
                    $fallback_query = DB::table('shft_veh')
                        ->join('assigned_drivers', 'shft_veh.vehicle_id', '=', 'assigned_drivers.vehicle_id')
                        ->select('assigned_drivers.driver_id')
                        ->where('shft_veh.shft_id', $shift_id)
                        ->where('shft_veh.status', 1)
                        ->where('assigned_drivers.status', 1)
                        ->limit(1)
                        ->get();
                    if(count($fallback_query) > 0 && $fallback_query[0]->driver_id){
                        $data['driver'] = $fallback_query[0]->driver_id;
                        $driver_found = true;
                    }
                } catch (\Exception $e) {
                    // Log error but don't fail the entire request
                    Log::warning('Failed to get driver information: ' . $e->getMessage());
                }
            }
            $data['rate_categories'] = DB::table('shipment_rate_categories')
                ->select('id', 'name')
                ->where('status', 1)
                ->get();
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Shipment details retrieved for Assign Vehicle successfully.',
            'data' => $data
        ], 200);
        
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve assign vehicle data.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getHistoryAndDocuments(GetHistoryAndDocumentsRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $input = $request->all();
            $shift_id = $input['shift_id'];
            $order_id = $input['order_id'];
            $stop_id = $input['stop_id'];
            $pickup_id = $input['pickup_id'];
            $report_id = isset($input['report_id'])??'';
            $curtz = $user->usr_tzone['timezone'] ?? 'UTC';
            $data = [];

            $whr = " AND status = 1";
            if (isset($report_id) && $report_id == 1) {
                $whr = " ";
            }
            $dlat = $dlng = $slat = $slng = "";
            $shift = DB::table('shipment')
                ->select('slat', 'slng', 'elat', 'elng')
                ->where('id', $shift_id)
                ->first();

            if ($shift) {
                $dlat = $shift->elat;
                $dlng = $shift->elng;
                $slat = $shift->slat;
                $slng = $shift->slng;
            }
            $tripQuery = Trip::select('id', 'vehicle_id', 'dlat as latitude', 'dlng as longitude')
                ->where('shift_id', $shift_id)
                ->orderBy('id', 'desc');

            $trip = $tripQuery->first();

            if ($trip) {
                $trip_id = $trip->id;
                $vehicle_id = $trip->vehicle_id;
                if ($trip->status == 1) {
                    $vehicle = TrucksData::select('latitude', 'longitude')
                        ->where('id', $vehicle_id)
                        ->first();
                    if ($vehicle) {
                        $slat = $vehicle->latitude;
                        $slng = $vehicle->longitude;
                    }
                } else {
                    $slat = $trip->latitude;
                    $slng = $trip->longitude;
                }
                $data["drivers"] = [];
                $vehicles = ShiftVehicle::select('vehicle_id')
                    ->where('shft_id', $shift_id)
                    ->where('status', 1)
                    ->groupBy('vehicle_id')
                    ->get();
                $vehicles = $vehicles->pluck('vehicle_id')->toArray();
                $vs = [];
                $chk = array();
                $primary_driver = "";

                foreach ($vehicles as $veh) {
                    $vs[] = $veh;
                }
                if (count($vehicles) > 0) {
                    // Get assigned drivers
                    $assignedDrivers = DB::table('assigned_drivers as td')
                        ->leftJoin('truck_drivers as d', 'd.id', '=', 'td.driver_id')
                        ->select('d.id', 'd.name', 'd.contact_num', 'td.created_at as createdon')
                        ->whereIn('td.vehicle_id', $vs)
                        ->where('td.status', '1')
                        ->groupBy('d.id', 'd.name', 'd.contact_num', 'td.created_at')
                        ->orderBy('td.created_at', 'asc')
                        ->get();

                    foreach ($assignedDrivers as $ar1) {
                        $primary_driver = $ar1->id;
                        if (!in_array($ar1->id, $chk)) {
                            $chk[] = $ar1->id;
                            $data["drivers"][] = [
                                'id' => $ar1->id,
                                'name' => $ar1->name,
                                'contact_num' => $ar1->contact_num,
                                'createdon' => $ar1->createdon
                            ];
                        }
                    }

                    // Get trip drivers (if trip exists)
                    if (isset($trip_id) && $trip_id > 0) {
                        $tripDrivers = DB::table('trip_drivers as td')
                            ->leftJoin('truck_drivers as d', 'd.id', '=', 'td.driver_id')
                            ->select('d.id', 'd.name', 'd.contact_num', 'td.created_at as createdon')
                            ->where('td.trip_id', $trip_id)
                            ->where('td.status', '1')
                            ->where('td.driver_id', '!=', $primary_driver)
                            ->groupBy('d.id', 'd.name', 'd.contact_num', 'td.created_at')
                            ->orderBy('td.created_at', 'asc')
                            ->get();

                        foreach ($tripDrivers as $ar) {
                            if (!in_array($ar->id, $chk)) {
                                $chk[] = $ar->id;
                                $data["drivers"][] = [
                                    'id' => $ar->id,
                                    'name' => $ar->name,
                                    'contact_num' => $ar->contact_num,
                                    'createdon' => $ar->createdon
                                ];
                            }
                        }
                    }
                }
                $data["primary_driver"] = $primary_driver;
            } else {
                $trip_id = 0;
                $data["drivers"] = [];
                $vehicles = ShiftVehicle::select('vehicle_id')
                    ->where('shft_id', $shift_id)
                    ->where('status', 1)
                    ->groupBy('vehicle_id')
                    ->get();
                $vehicles = $vehicles->pluck('vehicle_id')->toArray();
                $vs = [];
                $primary_driver = "";
                $chk = array();
                foreach ($vehicles as $veh) {
                    $vs[] = $veh;
                }
                if (count($vehicles) > 0) {
                    // Get assigned drivers
                    $assignedDrivers = DB::table('assigned_drivers as td')
                        ->leftJoin('truck_drivers as d', 'd.id', '=', 'td.driver_id')
                        ->select('d.id', 'd.name', 'd.contact_num', 'td.created_at as createdon')
                        ->whereIn('td.vehicle_id', $vs)
                        ->where('td.status', '1')
                        ->groupBy('d.id', 'd.name', 'd.contact_num', 'td.created_at')
                        ->orderBy('td.created_at', 'asc')
                        ->get();

                    foreach ($assignedDrivers as $ar1) {
                        $primary_driver = $ar1->id;
                        if (!in_array($ar1->id, $chk)) {
                            $chk[] = $ar1->id;
                            $data["drivers"][] = [
                                'id' => $ar1->id,
                                'name' => $ar1->name,
                                'contact_num' => $ar1->contact_num,
                                'createdon' => $ar1->createdon
                            ];
                        }
                    }

                    // Get vehicle drivers
                    $vehicleDrivers = DB::table('vehicles_drivers as td')
                        ->leftJoin('truck_drivers as d', 'd.id', '=', 'td.driver_id')
                        ->select('d.id', 'd.name', 'd.contact_num', 'td.created_at as createdon')
                        ->whereIn('td.vehicle_id', $vs)
                        ->where('td.status', '1')
                        ->where('td.driver_id', '!=', $primary_driver)
                        ->groupBy('d.id', 'd.name', 'd.contact_num', 'td.created_at')
                        ->orderBy('td.created_at', 'asc')
                        ->get();

                    foreach ($vehicleDrivers as $ar) {
                        if (!in_array($ar->id, $chk)) {
                            $chk[] = $ar->id;
                            $data["drivers"][] = [
                                'id' => $ar->id,
                                'name' => $ar->name,
                                'contact_num' => $ar->contact_num,
                                'createdon' => $ar->createdon
                            ];
                        }
                    }
                }
                $data["primary_driver"] = $primary_driver;
            }
            $sql = null;
            $sql1 = null;
            $pods = [];

            if (strlen($stop_id) > 0 && strlen($pickup_id) > 0 && strlen($order_id) == 0) {
                $sql = DB::table('stop_status as ts')
                    ->leftJoin('status_master as sm', 'sm.id', '=', 'ts.status_id')
                    ->leftJoin('shiporder_stop_sequence as e', function($join) {
                        $join->on('e.shift_id', '=', 'ts.shipment_id')
                            ->where(function($query) {
                                $query->whereColumn('e.stop_id', 'ts.stop_id')
                                    ->orWhereColumn('e.drop_stopid', 'ts.stop_id');
                            });
                    })
                    ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                            'sm.status_name', 'ts.status_code',
                            DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                            'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                    ->where('ts.shipment_id', $shift_id)
                    ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                            'sm.status_name', 'ts.status_code', 'ts.created_at', 'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                    ->orderBy('ts.created_at', 'asc')
                    ->get();

                $sql1 = DB::table('pod_uploads as ts')
                    ->leftJoin('document_types as dt', 'dt.id', '=', 'ts.doc_type')
                    ->leftJoin('shiporder_stop_sequence as e', 'e.id', '=', 'ts.stop_seq_id')
                    ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                            'dt.type_name', 'ts.createdby', 'ts.created_at as createdon', 'ts.imgpath',
                            'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                    ->where('ts.stop_seq_id', $pickup_id)
                    ->where('ts.shipment_id', $shift_id)
                    ->where('ts.stop_id', $stop_id)
                    ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                            'dt.type_name', 'ts.createdby', 'ts.created_at', 'ts.imgpath',
                            'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                    ->orderBy('ts.created_at', 'asc')
                    ->get();

                $emp = DB::table('shiporder_stop_sequence')
                    ->select('plat', 'plng', 'dlat', 'dlng', 'stop_id')
                    ->where('id', $pickup_id)
                    ->where(function($query) use ($stop_id) {
                        $query->where('stop_id', $stop_id)
                            ->orWhere('drop_stopid', $stop_id);
                    })
                    ->first();
                if ($emp) {
                    if ($emp->stop_id == $stop_id) {
                        $dlat = $emp->plat;
                        $dlng = $emp->plng;
                    } else {
                        $dlat = $emp->dlat;
                        $dlng = $emp->dlng;
                    }
                    if ($trip_id == 0) {
                        if ($emp->stop_id == $stop_id) {
                            $slat = $emp->plat;
                            $slng = $emp->plng;
                        }
                    }
                }
            } elseif (strlen($order_id) > 0) {
                $sql = DB::table('stop_status as ts')
                    ->leftJoin('orders as o', 'o.id', '=', 'ts.order_id')
                    ->leftJoin('status_master as sm', 'sm.id', '=', 'ts.status_id')
                    ->leftJoin('shiporder_stop_sequence as e', function($join) {
                        $join->on('e.order_id', '=', 'o.order_id')
                            ->where(function($query) {
                                $query->whereColumn('e.stop_id', 'ts.stop_id')
                                    ->orWhereColumn('e.drop_stopid', 'ts.stop_id');
                            });
                    })
                    ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                            'sm.status_name', 'ts.status_code', 'ts.comment',
                            DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                            'e.address', 'o.order_id', 'e.pickup', 'e.drop')
                    ->where('o.order_id', $order_id)
                    ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                            'sm.status_name', 'ts.status_code', 'ts.comment', 'ts.created_at',
                            'e.address', 'o.order_id', 'e.pickup', 'e.drop')
                    ->orderBy('ts.created_at', 'asc')
                    ->get();

                $sql1 = DB::table('pod_uploads as ts')
                    ->leftJoin('document_types as dt', 'dt.id', '=', 'ts.doc_type')
                    ->leftJoin('shiporder_stop_sequence as e', 'e.id', '=', 'ts.stop_seq_id')
                    ->leftJoin('orders as o', 'o.id', '=', 'ts.order_id')
                    ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                            'dt.type_name', 'ts.createdby',
                            DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                            'ts.imgpath', 'e.address', 'o.order_id', 'e.pickup', 'e.drop')
                    ->where('o.order_id', $order_id)
                    ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                            'dt.type_name', 'ts.createdby', 'ts.created_at', 'ts.imgpath',
                            'e.address', 'o.order_id', 'e.pickup', 'e.drop')
                    ->orderBy('ts.created_at', 'asc')
                    ->get();
            } else {
                if (strlen($stop_id) > 0) {
                    $sql = DB::table('stop_status as ts')
                        ->leftJoin('status_master as sm', 'sm.id', '=', 'ts.status_id')
                        ->leftJoin('shiporder_stop_sequence as e', function($join) {
                            $join->on('e.shift_id', '=', 'ts.shipment_id')
                                ->where(function($query) {
                                    $query->whereColumn('e.stop_id', 'ts.stop_id')
                                        ->orWhereColumn('e.drop_stopid', 'ts.stop_id');
                                });
                        })
                        ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                                'sm.status_name', 'ts.status_code',
                                DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                                'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                        ->where('ts.shipment_id', $shift_id)
                        ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                                'sm.status_name', 'ts.status_code', 'ts.created_at',
                                'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                        ->orderBy('ts.created_at', 'asc')
                        ->get();

                    $sql1 = DB::table('pod_uploads as ts')
                        ->leftJoin('document_types as dt', 'dt.id', '=', 'ts.doc_type')
                        ->leftJoin('shiporder_stop_sequence as e', 'e.id', '=', 'ts.stop_seq_id')
                        ->leftJoin('shiporder_stops as ss', function($join) {
                            $join->where(function($query) {
                                $query->whereColumn('ss.id', 'e.drop_stopid')
                                    ->orWhereColumn('ss.id', 'e.stop_id');
                            });
                        })
                        ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                                'dt.type_name', 'ts.createdby',
                                DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                                'ts.imgpath', 'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                        ->where('ts.stop_id', $stop_id)
                        ->where('ts.shipment_id', $shift_id)
                        ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                                'dt.type_name', 'ts.createdby', 'ts.created_at', 'ts.imgpath',
                                'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                        ->orderBy('ts.created_at', 'asc')
                        ->get();

                    $stops = DB::table('shiporder_stops')
                        ->where('shipment_id', $shift_id)
                        ->where('id', '>=', $stop_id)
                        ->orderBy('id', 'asc')
                        ->limit(2)
                        ->get();
                    if ($stops->count() > 0) {
                        $stopsArray = $stops->toArray();
                        $slat = $stopsArray[0]->plat;
                        $slng = $stopsArray[0]->plng;
                        if ($trip_id == 0) {
                            if (count($stopsArray) > 1) {
                                $slat = $stopsArray[0]->plat;
                                $slng = $stopsArray[0]->plng;
                                $dlat = $stopsArray[1]->plat;
                                $dlng = $stopsArray[1]->plng;
                            } else {
                                $slat = $stopsArray[0]->plat;
                                $slng = $stopsArray[0]->plng;
                            }
                        }
                    }
                } else {
                    $sql = DB::table('stop_status as ts')
                        ->leftJoin('status_master as sm', 'sm.id', '=', 'ts.status_id')
                        ->leftJoin('shiporder_stop_sequence as e', function($join) {
                            $join->on('e.shift_id', '=', 'ts.shipment_id')
                                ->where(function($query) {
                                    $query->whereColumn('e.stop_id', 'ts.stop_id')
                                        ->orWhereColumn('e.drop_stopid', 'ts.stop_id');
                                });
                        })
                        ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                                'sm.status_name', 'ts.status_code', 'ts.comment',
                                DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                                'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                        ->where('ts.shipment_id', $shift_id)
                        ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                                'sm.status_name', 'ts.status_code', 'ts.comment', 'ts.created_at',
                                'e.address', 'e.order_id', 'e.pickup', 'e.drop')
                        ->orderBy('ts.created_at', 'asc')
                        ->get();

                    $sql1 = DB::table('pod_uploads as ts')
                        ->leftJoin('document_types as dt', 'dt.id', '=', 'ts.doc_type')
                        ->leftJoin('shiporder_stop_sequence as e', 'e.id', '=', 'ts.stop_seq_id')
                        ->leftJoin('orders as o', 'o.id', '=', 'ts.order_id')
                        ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                                'dt.type_name', 'ts.createdby',
                                DB::raw("convertToClientTZ(ts.created_at, 'UTC') as createdon"),
                                'ts.imgpath', 'e.address', 'o.order_id', 'e.pickup', 'e.drop')
                        ->where('ts.shipment_id', $shift_id)
                        ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.stop_id', 'ts.stop_type',
                                'dt.type_name', 'ts.createdby', 'ts.created_at', 'ts.imgpath',
                                'e.address', 'o.order_id', 'e.pickup', 'e.drop')
                        ->orderBy('ts.created_at', 'asc')
                        ->get();
                }
            }

            // Process POD uploads if available
            if ($sql1 && $sql1->count() > 0) {
                $i = 1;
                foreach ($sql1 as $eachRow) {
                    $base64DocumentData = $this->getBase64DocumentData($eachRow->imgpath);

                    $pods[] = [
                        'sno' => $i,
                        'latitude' => $eachRow->latitude,
                        'longitude' => $eachRow->longitude,
                        'type_name' => $eachRow->type_name,
                        'driver_name' => $this->getDrivernameById($eachRow->createdby)["name"],
                        'stop_id' => $eachRow->stop_id,
                        'stop_type' => $eachRow->stop_type,
                        'createdon' => Carbon::parse($eachRow->createdon)->format("d M,y h:i A"),
                        'extention' => $base64DocumentData['extention'],
                        'file_content' => $base64DocumentData['file_content'],
                    ];
                    $i++;
                }
            }
            $data["pods"] = $pods;
            $history = $sql && $sql->count() > 0 ? $sql->toArray() : [];
            $data["history"] = $this->getCustomStatusForOrders($history, $shift_id, $curtz, $order_id);
            $data["slat"] = $slat ?? '';
            $data["slng"] = $slng ?? '';
            $data["dlat"] = $dlat ?? '';
            $data["dlng"] = $dlng ?? '';

            return response()->json([
                'status' => 'success',
                'message' => 'History and documents retrieved successfully.',
                'data' => $data
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve shift details.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    function getBase64DocumentData(string $documentName): array
    {
        $filePath = './assets/poduploads/' . $documentName;
        if (file_exists($filePath)) {
            $extention = strtolower(pathinfo($documentName, PATHINFO_EXTENSION));
            $fileData = base64_encode(file_get_contents($filePath));
            $fileContent = 'data: ' . mime_content_type($filePath) . ';base64,' . $fileData;
        }
        return ['extention' => $extention ?? '', 'file_content' => $fileContent ?? ''];
    }

    function getDrivernameById($id){
        $res = array("name"=>"","contact_num"=>"","imei"=>"");
        if($id != ""){
            $chk = Driver::select('name', 'contact_num')->where('id', $id)->first();
            if($chk){
                $chk = $chk->toArray();
                if(count($chk)>0){
                    $res = array("name"=>$chk["name"],"contact_num"=>$chk["contact_num"],"imei"=>'');
                }
            }
        }
        return $res;
    }

    public function getCustomStatusForOrders(array $history, int $shiftId, string $userTimZone, string $order_id = null): array
    {
        $statusCodes = $orderIds = $orderDetails = $remainingStatuses = [];
        foreach ($history as $eachRow) {
            $statusCodes[] = $eachRow['status_code'];
        }
        if (!in_array('1500', $statusCodes)) {
            $remainingStatuses[] = '1500';
        }
        if (!in_array('1600', $statusCodes)) {
            $remainingStatuses[] = '1600';
        }
        if (empty($remainingStatuses)) {
            return $history;
        }
        if($order_id != null){
            $getOrderIds = DB::table('orders')
                ->select('id', 'order_id')
                ->where('order_id', $order_id)
                ->where('status', '>', 0)
                ->get()
                ->toArray();
        } else {
            $getOrderIds = DB::table('orders')
                ->select('id', 'order_id')
                ->where('shift_id', $shiftId)
                ->where('status', '>', 0)
                ->get()
                ->toArray();
        }
        foreach ($getOrderIds as $eachRow) {
            $orderIds[] = $eachRow['id'];
            $orderDetails[$eachRow['id']] = $eachRow['order_id'];
        }
        if (empty($orderIds)) {
            return $history;
        }
        $getStatuses = DB::table('stop_status')
            ->select('id', 'order_id', 'loc_name', 'status_code', 'comment', DB::raw("convertToClientTZ(created_at, 'UTC') as createdon"))
            ->whereIn('order_id', $orderIds)
            ->where('status', 1)
            ->whereIn('status_code', $remainingStatuses)
            ->get()
            ->toArray();
        foreach ($getStatuses as $eachStatus) {
            $history[] = ['order_id' => $orderDetails[$eachStatus['order_id']] ?? $eachStatus['order_id'], 'status_code' => $eachStatus['status_code'], 'status_name' => $eachStatus['status_code'] == "1500" ? "Customs Clearance Strated" : "Customs Clearance Completed at Border", 'comment' => $eachStatus['comment'], 'loc_name' => $eachStatus['loc_name'], 'latitude' => '', 'longitude' => '', 'stop_id' => '', 'stop_type' => '', 'createdon' => $eachStatus['createdon']];
        }
        return $history;
    }

    public function billingDetails(BillingDetailsRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get shift_id from validated request
            $shift_id = $request->input('shift_id');

            // Initialize data array
            $data = [];

            // Fetch billing information
            $bills = DB::table('bills as b')
                ->leftJoin('billgroup_master as g', 'g.id', '=', 'b.bill_group')
                ->leftJoin('billstatus_master as s', 's.id', '=', 'b.invoice_status')
                ->leftJoin('reveneus as r', 'r.bill_id', '=', 'b.id')
                ->leftJoin('orders as o', 'o.id', '=', 'r.order_id')
                ->select(
                    'b.id',
                    'b.bill_party',
                    'b.recipient_type',
                    'r.invoice_number',
                    'r.invoice_date',
                    'b.customer_code',
                    'g.name as billgroup',
                    's.name as billstatus'
                )
                ->whereIn('b.status', [1, 2, 3, 4])
                ->where('r.status', 1)
                ->where('o.shift_id', $shift_id)
                ->orderBy('b.id', 'DESC')
                ->get();

            $data['bills'] = $bills->toArray();

            // Fetch billing documents
            $files = DB::table('billing_documents')
                ->select('id', 'shift_id', 'file_name')
                ->where('shift_id', $shift_id)
                ->where('status', 1)
                ->get();

            $data['files'] = $files->toArray();

            return response()->json([
                'status' => 'success',
                'message' => 'Billing details retrieved successfully.',
                'data' => $data
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve billing details.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function secureLink(SecureLinkRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $data = [];
            $vendor_id = $request->input("vendor_id");
            $shipmentid = $request->input("shipmentid");
            $voyage_number = $request->input("voyage_number");

            $errors = [];

            // Expiry of token after 10 days
            $expDate = strtotime('+10 days');
            if ($expDate === false) {
                $expDate = time() + (10 * 24 * 60 * 60); // 10 days in seconds
            }
            $jwt_claim = ["sub" => $shipmentid, "exp" => $expDate, 'iss' => 'kn' . $vendor_id];
            $get_jwt = $this->generate_jwt($jwt_claim);
            if (empty($get_jwt)) {
                $errors[] = "Failed to generate JWT token. Missing JWT signature key in configuration";
            } else {
                $data["link_id"] = config('app.url') . "driverappv3/" . $get_jwt;
            }
            $data["voyage_number"] = $voyage_number;

            // Carrier Secure Link
            $carrier_jwt_claim = ['iss' => "kn" . $vendor_id, "sub" => $shipmentid];
            $carrier_get_jwt = $this->generate_jwt($carrier_jwt_claim, 'carrier');
            if (empty($carrier_get_jwt)) {
                $errors[] = "Failed to generate carrier JWT token. Missing JWT signature key in configuration";
            } else {
                $data["carrier_link_id"] = config('app.url') . "knmcdbkg/" . $carrier_get_jwt;
            }
            $data['errors'] = $errors;
            return response()->json([
                'status' => 'success',
                'message' => 'Secure link generated successfully.',
                'data' => $data
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate secure link.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function generate_jwt(array $data, string $sourceType = 'driver'): string
    {
        try {
            $signatureKey = $this->getSignatureKey($sourceType);
            if (empty($signatureKey)) {
                return '';
            }
            
            $claim = $data;
            $currentTime = time();
            $claim["iat"] = $currentTime;
            $claim["exp"] = $claim['exp'] ?? ($currentTime + 86400);
            $claim["iss"] = $claim['iss'] ?? "kn";
            
            $headerJson = json_encode(["typ" => "JWT", "alg" => "HS256"]);
            $payloadJson = json_encode($claim);
            
            if ($headerJson === false || $payloadJson === false) {
                return '';
            }
            
            $base64_header = $this->base64Encode($headerJson);
            $base64_payload = $this->base64Encode($payloadJson);
            
            $signature = hash_hmac('sha256', $base64_header . "." . $base64_payload, $signatureKey, true);
            
            if ($signature === false) {
                return '';
            }
            
            $base64_signature = $this->base64Encode($signature);
            
            return $base64_header . "." . $base64_payload . "." . $base64_signature;
        } catch (\Exception $e) {
            return '';
        }
    }

    public function base64Encode(string $text): string
    {
        try {
            $encoded = base64_encode($text);
            return str_replace(['/', '+', '=', '-', '_'], '', $encoded);
        } catch (\Exception $e) {
            return '';
        }
    }

    public function getSignatureKey(string $sourceType = ''): string
    {
        $validSourceTypes = ['driver', 'carrier'];
        if (!in_array($sourceType, $validSourceTypes, true)) {
            return '';
        }
        
        $configKey = ($sourceType === 'driver') 
            ? config('services.jwt_signature') 
            : config('services.jwt_signature_carrier');
            
        return $configKey ?? '';
    }

    public function calculateEmissions(CalculateEmissionsRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get validated parameters
            $weight = $request->input('weight', 1);
            $slat = $request->input('slat');
            $slng = $request->input('slng');
            $elat = $request->input('elat');
            $elng = $request->input('elng');

            // Calculate distance
            $distance = $this->distancemetrixship($slat, $slng, $elat, $elng);
            $distanceVal = preg_replace('/[^0-9.]/', '', trim($distance['disttext']));
            
            if (empty($distanceVal)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid distance calculated for emission.',
                    'data' => null,
                ], 400);
            }

            $distanceInMiles = ceil($distanceVal);
            $weightInTons = ($weight == 0 || $weight == "") ? 1 : $weight;

            // Calculate CO2 emissions
            // Formula: 161.8 grams of CO2 per ton-mile
            $gramsOfCo2PerTonMile = 161.8;
            $totalTonMiles = $distanceInMiles * $weightInTons;
            $co2EmissionInGrams = $totalTonMiles * $gramsOfCo2PerTonMile;
            $co2EmissionInGrams = floatval($co2EmissionInGrams);

            $emissionData = [
                'weight' => $weightInTons,
                'distance' => $distanceInMiles,
                'emission' => $co2EmissionInGrams
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'Emissions calculated successfully.',
                'data' => $emissionData
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to calculate emissions.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    function distancemetrixship($slat, $slng, $elat, $elng, $origin = '', $destination = '')
	{
		$res = array();
		$res['distance'] = $res['disttext'] = $res['duration'] = $res['duratext'] = "";
		if($origin != '' && $destination != ''){
            // Haversine formula to calculate distance and duration (deprecated)
            $query = "SELECT 
                    3959 * ACOS(
                        SIN(RADIANS($slat)) * SIN(RADIANS($elat)) +
                        COS(RADIANS($slat)) * COS(RADIANS($elat)) * 
                        COS(RADIANS($elng - $slng))
                    ) AS dist_in_miles,
                    
                    6371 * ACOS(
                        SIN(RADIANS($slat)) * SIN(RADIANS($elat)) +
                        COS(RADIANS($slat)) * COS(RADIANS($elat)) *
                        COS(RADIANS($elng - $slng))
                    ) AS dist_in_kms,
                    
                    (6371 * ACOS(
                        SIN(RADIANS($slat)) * SIN(RADIANS($elat)) +
                        COS(RADIANS($slat)) * COS(RADIANS($elat)) *
                        COS(RADIANS($elng - $slng))
                    )) / 60 * 60 AS duration_in_mins";

            // PostGIS query to calculate distance and duration
			// $query = "SELECT 
            //                 (ST_Distance(origin::geography, destination::geography) / 1609.34) AS dist_in_miles,
            //                 (ST_Distance(origin::geography, destination::geography) / 1000) AS dist_in_kms, 
            //                 (ST_Distance(origin::geography, destination::geography) / (60 * 1000 / 3600)) / 60 AS duration_in_mins
            //             FROM (
            //                 SELECT 
            //                     ST_GeomFromText('" . $origin . ")', 4326) AS origin,
            //                     ST_GeomFromText('" . $destination . ")', 4326) AS destination
            //             ) AS points;
            //             ";
			$results = DB::select($query);
			$res['disttext'] = $results[0]->dist_in_miles;
			$res['distance'] = $results[0]->dist_in_kms;
			$res['duratext'] = $results[0]->duration_in_mins;
			$res['duration'] = $results[0]->duration_in_mins;
		} else if ($slat != "" && $slng != "" && $elat != "" && $elng != "" && $res['distance'] == "" && $res['duration'] == "") {
			$origin = "POINT( " . $slng . " " . $slat . ")";
			$destination = "POINT( " . $elng . " " . $elat . ")";
			return $this->distancemetrixship($slat, $slng, $elat, $elng, $origin, $destination);
		}
		return $res;
	}

    private function getUserTimeZone($uid, $cmpcode)
    {
        $getqry = DB::table('country_master')
            ->select('country_code', 'cntry_timezone', 'cntry_hrs', 'currency')
            ->where(function ($query) use ($cmpcode) {
                $query->where('country_code', $cmpcode)
                    ->orWhere('country_name', $cmpcode);
            })
            ->where('status', 1)
            ->first();

        $res = array("currency" => "SGD", "timezone" => "Asia/Singapore", "hrs" => "+08.00", "country" => "SG");
        if ($getqry) {
            $res = array(
                "currency" => $getqry->currency,
                "timezone" => $getqry->cntry_timezone,
                "hrs" => $getqry->cntry_hrs,
                'country' => $getqry->country_code
            );
        }
        return $res;
    }

    public function tripEtnshipment(TripEtnshipmentRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $user_id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($user_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            $shiftID = $request->input('shift_id');
            $tripId = $request->input('trip_id', '');

        // Initialize all data keys with defaults
        $data = [
            'last_stop' => ['createdon' => NULL],
            'vehicle' => [],
            'record' => [],
            'records' => [], // Ensure records is always initialized
            'hos' => 'N/A',
            'alarm_count' => 0,
            'trip_id' => 0,
            'travel_dist' => 0.00,
            'isSimBased' => "false",
            'drivers' => [],
            'driver' => [],
            'ref' => '',
            'pings' => 0,
            'geofencests' => 'OUT',
            'etime' => 'N/A',
            'flag' => false,
            'routeMapDetails' => [],
            'order' => (object) [
                'order_id' => '',
                'trip_id' => 0,
                'user_id' => 0,
                'shift_id' => 0,
                'plat' => 0,
                'plng' => 0,
                'dlat' => 0,
                'dlng' => 0,
                'pickup_endtime' => '',
                'drop_endtime' => '',
                'id' => 0,
                'pickup_address1' => '',
                'pickup_city' => '',
                'pickup_country' => '',
                'pickup_pincode' => '',
                'delivery_address1' => '',
                'delivery_city' => '',
                'delivery_country' => '',
                'delivery_pincode' => ''
            ],
            'timezone' => 'UTC',
            'trip' => [],
            'tripicdes' => (object) [],
            'order_id' => '',
            'lguid' => 0,
            'id' => 0,
            'trip_id' => $tripId,
            'shift_id' => $shiftID
        ];

        if ($shiftID != "") {
            try {
                $multiTripType = 1;
                $userdetails = DB::table('sx_users')
                    ->select('id', 'country_code')
                    ->where('id', $user_id)
                    ->first();
                $tz = $this->getUserTimeZone($userdetails->id, $userdetails->country_code);
                $data["timezone"] = $tz['timezone'] ?? 'UTC';
                $curtz = $tz['timezone'];
                $data["trip"] = [];
                $stopstatushist = DB::table('stop_status as ts')
                    ->leftJoin('status_master as sm', 'sm.id', '=', 'ts.status_id')
                    ->leftJoin('shiporder_stops as ss', 'ss.id', '=', 'ts.stop_id')
                    ->select('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                            'ts.status_code', 'ts.status_id',
                            DB::raw("convertToClientTZ(ts.created_at, '$curtz') as createdon"),
                            'sm.status_name', 'ss.startdate', 'ss.enddate')
                    ->where('ts.shipment_id', $shiftID)
                    ->groupBy('ts.id', 'ts.latitude', 'ts.longitude', 'ts.loc_name', 'ts.stop_id', 'ts.stop_type',
                                'sm.status_name', 'ts.status_code', 'ts.status_id', 'createdon', 'ss.startdate', 'ss.enddate')
                    ->orderBy('ts.created_at', 'DESC')
                    ->get();
                if ($stopstatushist->isNotEmpty()) {
                    $stopstatushistData = $stopstatushist->first();
                    $data["stopstatushist"] = $stopstatushistData;
                } else {
                    $data["stopstatushist"] = [];
                }

                $ord1 = Order::where('shift_id', $shiftID)
                    ->select('id')
                    ->orderBy('id', 'desc')
                    ->first();
                
                if (empty($ord1)) {
                    // $getLegOrd = Order::whereRaw('FIND_IN_SET(?, order_trips) > 0', [$shiftID])
                    $getLegOrd = Order::whereIn('shift_id', [$shiftID])
                                ->select('id')
                                ->get();
                                
                    if ($getLegOrd->count() > 0) {
                        $ord1 = $getLegOrd->first();
                        $multiTripType = 2;
                    }
                }
                $data['containerNumber'] = 0;
                
                // Get shipment data directly even if no order exists
                $shipmentData = DB::table('shipment')
                    ->where('id', $shiftID)
                    ->where('org_id', $org_id)
                    ->first();
                
                if ($shipmentData) {
                    $data["tripicdes"] = (object) $shipmentData;
                    $routeTemplateId = $shipmentData->routetemplate_id ?? 0;
                    if ($routeTemplateId > 0) {
                        $getContainerNo = DB::table('route_templates')
                            ->select('container_number')
                            ->where('id', $routeTemplateId)
                            ->where('status', 1)
                            ->first();
                        $data['containerNumber'] = $getContainerNo->container_number ?? 0;
                    }
                }
                
                if (!empty($ord1)) {
                    $ismaintrip = [];
                    // Check if shift_leg_id is 0 for the given shift_id
                    $main_trip = DB::table('shipment')
                        ->select('id')
                        ->where('id', $shiftID)
                        ->where('shift_leg_id', 0)
                        ->first();

                    // If a record is found where shift_leg_id is 0
                    if ($main_trip) {
                        $ismaintrip = DB::table('shipment')
                            ->select('id', 'shipmentid')
                            ->where('shift_leg_id', $shiftID)
                            ->get()
                            ->toArray();
                    }
                    $data['ismaintrip'] = $ismaintrip;
                    $data["id"] = $ord1->id;
                    
                    $ord = DB::table('orders')
                        ->where('id', $ord1->id)
                        ->first();
                    
                    if ($ord) {
                        $data["order"] = $ord;
                        if ($ord->trip_id) {
                            $tb_trip_alerts = DB::table('trip_alerts')->where('trip_id', $ord->trip_id)->count();
                            $tb_night_drive = DB::table('night_drive')->where('trip_id', $ord->trip_id)->count();
                            $tb_over_speed = DB::table('over_speed')->where('trip_id', $ord->trip_id)->count();
                            $data['alarm_count'] = $tb_trip_alerts; // + $tb_night_drive + $tb_over_speed;
                        }
                        if ($multiTripType == 2) {
                            $ord->shift_id = $shiftID;
                        }
                        
                        $shiftData = DB::table('shipment')
                            ->where('id', $ord->shift_id)
                            ->where('org_id', $org_id)
                            ->first();
                        
                        $data["tripicdes"] = $shiftData ?: (object) [];
                        $routeTemplateId = $shiftData->routetemplate_id ?? 0;
                        $containerNumber = 0;

                        if ($routeTemplateId > 0) {
                            $getContainerNo = DB::table('route_templates')
                                ->select('container_number')
                                ->where('id', $routeTemplateId)
                                ->where('status', 1)
                                ->first();

                            $containerNumber = $getContainerNo->container_number ?? 0;
                        }
                        $data['containerNumber'] = $containerNumber;
                        $data["order_id"] = $ord->order_id;
                        $oid = $ord->id;
                        $data['lguid'] = $uid = $ord->user_id;
                        
                        $user = DB::table('sx_users')
                            ->select('id', 'country_code')
                            ->where('id', $uid)
                            ->first();
                        
                        $tz = $this->getUserTimeZone($user->id, $user->country_code);
                        $data["timezone"] = $tz['timezone'] ?? 'UTC';
                        $curtz = $tz['timezone'];
                        $data["trip"] = [];

                        $tripinfo = $this->chkTripinfoByOrdertrip($ord->trip_id, $data["timezone"]);

                        if (count($tripinfo) > 0) {
                            $data["trip_id"] = $trip_id = $tripinfo['id'];
                            $data["trip"] = $trip = $tripinfo;
                            $data["driver"] = $driver_row = [];
                            $data['last_stop'] = [];
                            $data["vehicle"] = $data["record"] = [];
                            
                            $vehicle = DB::table('trucks_data')
                                ->select('register_number', 'latitude', 'longitude', 'speed', 'battery', 'receivedon as timestamp', 'truck_number')
                                ->where('id', $trip['vehicle_id'])
                                ->first();
                            
                            $hour1 = $hour2 = 0;
                            if ($vehicle) {
                                $data["vehicle"] = $data["record"] = (array) $vehicle;
                            }
                            
                            $last_stop = DB::select("SELECT convertToClientTZ(createdon, ?) as createdon FROM tb_stop_status WHERE shipment_id = ? ORDER BY id DESC LIMIT 1", [$data["timezone"], $tripinfo['shift_id']]);
                            if (!empty($last_stop)) {
                                $data['last_stop'] = (array) $last_stop[0];
                            }
                            
                            if ($trip['etime'] && $trip['stime']) {
                                $datetimeObj1 = new DateTime($trip['stime']);
                                $datetimeObj2 = new DateTime($trip['etime']);
                                $interval = $datetimeObj1->diff($datetimeObj2);
                                if ($interval->format('%a') > 0) {
                                    $hour1 = $interval->format('%a') * 24;
                                }
                                if ($interval->format('%h') > 0) {
                                    $hour2 = $interval->format('%h');
                                }
                                $data['hos'] = ($hour2 + $hour1) . " Hr";
                            } else {
                                if ($trip['stime']) {
                                    $datetimeObj1 = new DateTime($trip['stime']);
                                } else {
                                    $datetimeObj1 = new DateTime($ord->pickup_endtime);
                                }
                                $datetimeObj2 = new DateTime($ord->drop_endtime);
                                $interval = $datetimeObj1->diff($datetimeObj2);
                                if ($interval->format('%a') > 0) {
                                    $hour1 = $interval->format('%a') * 24;
                                }
                                if ($interval->format('%h') > 0) {
                                    $hour2 = $interval->format('%h');
                                }
                                $data['hos'] = ($hour2 + $hour1) . " Hr";
                            }

                            $rtdrivelocation = DB::table('rtdrive_locations')
                                ->select('latitude', 'longitude')
                                ->where('trip_id', $trip_id)
                                ->orderBy('id', 'DESC')
                                ->first();
                            
                            if ($rtdrivelocation) {
                                $data['etime'] = ['lat1'=>$rtdrivelocation->latitude, 'lng1'=>$rtdrivelocation->longitude, 'lat2'=>$trip['latitude'], 'lng2'=>$trip['longitude']];
                                // commented untill gmaps were integrated
                                // $distancemetrix = distancemetrixship($rtdrivelocation->latitude, $rtdrivelocation->longitude, $trip['latitude'], $trip['longitude']);
                                // if ($distancemetrix['duration']) {
                                //     $distancemetrix['duration'] = ($distancemetrix['duration'] == "") ? 0 : $distancemetrix['duration'];
                                //     $data['etime'] = secToHR($distancemetrix['duration']);
                                // } else {
                                //     if (!empty($data['vehicle']['register_number'])) {
                                //         $shift = DB::table('tb_shifts')
                                //             ->select('elat', 'elng', 'egeolocation')
                                //             ->where('id', $ord->shift_id)
                                //             ->first();
                                        
                                //         if ($shift) {
                                //             $truck = DB::table('tb_trucks_data')
                                //                 ->select('latitude', 'longitude')
                                //                 ->where('register_number', $data['vehicle']['register_number'])
                                //                 ->first();
                                            
                                //             if ($truck) {
                                //                 $distancemetrix = distancemetrixship($truck->latitude, $truck->longitude, $shift->elat, $shift->elng);
                                //                 if ($distancemetrix['duration']) {
                                //                     $distancemetrix['duration'] = ($distancemetrix['duration'] == "") ? 0 : $distancemetrix['duration'];
                                //                     $data['etime'] = secToHR($distancemetrix['duration']);
                                //                 } else {
                                //                     $data['etime'] = "N/A";
                                //                 }
                                //             } else {
                                //                 $data['etime'] = "N/A";
                                //             }
                                //         } else {
                                //             $data['etime'] = "N/A";
                                //         }
                                //     } else {
                                //         $data['etime'] = "N/A";
                                //     }
                                // }
                            } else {
                                $data['etime'] = ['lat1'=>$ord->plat, 'lng1'=>$ord->plng, 'lat2'=>$ord->dlat, 'lng2'=>$ord->dlng];
                                // commented untill gmaps were integrated
                                // $distancemetrix = distancemetrixship($ord->plat, $ord->plng, $ord->dlat, $ord->dlng);
                                // if ($distancemetrix['duration']) {
                                //     $distancemetrix['duration'] = ($distancemetrix['duration'] == "") ? 0 : $distancemetrix['duration'];
                                //     $data['etime'] = secToHR($distancemetrix['duration']);
                                // } else {
                                //     $data['etime'] = "N/A";
                                // }
                            }
                            
                            $drivdetail = DB::table('truck_drivers')
                                ->select('id', 'name', 'contact_num', 'track_type')
                                ->where('id', $trip['driver_id'])
                                ->first();
                            
                            if ($drivdetail) {
                                $data["driver"] = $driver_row = (array) $drivdetail;
                            }

                            $data["ref"] = "";
                            $refs = DB::table('order_references')
                                ->selectRaw('GROUP_CONCAT(ref_value) as ref')
                                ->where('order_id', $oid)
                                ->where('status', 1)
                                ->first();
                            
                            if ($refs) {
                                $data['ref'] = $refs->ref ?: '';
                            }
                            
                            $flag = $dist = 0;
                            $coordinates = [];
                            
                            if ($tripinfo['status'] == 1) {
                                $records = DB::table('rtdrive_locations')
                                    ->select('latitude', 'longitude', 'speed', 'battery', 'timestamp')
                                    ->where('trip_id', $trip_id)
                                    ->orderBy('timestamp', 'ASC')
                                    ->get();
                                
                                if ($records->isNotEmpty()) {
                                    $tstamp = 0;
                                    $lat = $lng = 0;
                                    foreach ($records as $row) {
                                        $coordinates[] = ['lat' => floatval($row->latitude), 'lng' => floatval($row->longitude), 'speed' => $row->speed, 'battery' => $row->battery, 'timestamp' => $row->timestamp];
                                        // if ($flag == 0) {
                                        //     $flag = 1;
                                        //     $lat = $row->latitude;
                                        //     $lng = $row->longitude;
                                        //     $tstamp = $row->timestamp;
                                        // } else {
                                        //     $a = calculateDistance12($lat, $lng, $row->latitude, $row->longitude);
                                        //     if ($tstamp != 0) {
                                        //         $datetime1 = new DateTime($tstamp);
                                        //         $datetime2 = new DateTime($row->timestamp);
                                        //         $interval = $datetime1->diff($datetime2);
                                        //         $time = $interval->format('%s');
                                        //         if ($time > 0) {
                                        //             $ratio = ($a * 1000) / $time;
                                        //         } else {
                                        //             $ratio = 0;
                                        //         }
                                        //         if ($ratio < 30 || $time < 300) {
                                        //             $dist = $dist + $a;
                                        //             $lat = $row->latitude;
                                        //             $lng = $row->longitude;
                                        //             $tstamp = $row->timestamp;
                                        //         }
                                        //     } else {
                                        //         $dist = $dist + $a;
                                        //     }
                                        // }
                                    }
                                }
                                $data["records"] = $coordinates;
                            } else {
                                $records = DB::table('rtdrive_locations')
                                    ->select('latitude', 'longitude', 'speed', 'battery', 'timestamp')
                                    ->where('trip_id', $trip_id)
                                    ->orderBy('timestamp', 'ASC')
                                    ->get();
                                
                                if ($records->isNotEmpty()) {
                                    foreach ($records as $row) {
                                        $coordinates[] = ['lat' => floatval($row->latitude), 'lng' => floatval($row->longitude), 'speed' => $row->speed, 'battery' => $row->battery, 'timestamp' => $row->timestamp];
                                    }
                                }
                                $data["records"] = $coordinates;
                                
                                $tripdist = DB::table('trip_summaries')
                                    ->select('trip_distance')
                                    ->where('trip_id', $trip_id)
                                    ->first();
                                
                                if ($tripdist) {
                                    $dist = $tripdist->trip_distance;
                                }
                            }
                            
                            $data["travel_dist"] = round($dist, 2);
                            $data["drivers"] = [];
                            
                            $drivers = DB::select("SELECT d.id,d.name,d.contact_num,convertToClientTZ(td.createdon, ?) as createdon,d.track_type,td.travelled_km FROM tb_trip_drivers td LEFT JOIN tb_truck_drivers d ON d.id = td.driver_id WHERE trip_id = ? AND td.status = '1' GROUP BY d.id ORDER BY td.createdon ASC", [$data["timezone"], $trip_id]);
                            
                            if (!empty($drivers)) {
                                $data["drivers"] = array_map(function($driver) {
                                    return (array) $driver;
                                }, $drivers);
                            }
                            
                            if (count($driver_row) > 0) {
                                $data["drivers"][] = array("id" => $driver_row['id'], "name" => $driver_row['name'], "contact_num" => $driver_row['name'], "createdon" => null, "track_type" => $driver_row['track_type'], "travelled_km" => $dist);
                            }
                            
                            if (count($data["drivers"]) > 0) {
                                if (isset($data["drivers"][count($data["drivers"]) - 1]["track_type"])) {
                                    if ($data["drivers"][count($data["drivers"]) - 1]["track_type"] == "1") {
                                        $data["isSimBased"] = "true";
                                    }
                                }
                            }
                            
                            $unit = 'K';
                            $geofence = DB::select("SELECT tt.id AS trpid,tt.plat,tt.plng,tt.shift_id,tg.routeid,tg.trip_id,tg.vehicle_id,tgr.id AS rid,tgr.maplat,tgr.maplng
                            FROM tb_trips AS tt 
                            INNER JOIN tb_geoassign AS tg ON tg.trip_id = tt.id
                            INNER JOIN tb_geofence_routes AS tgr ON tgr.id = tg.routeid 
                            WHERE tt.id = ? ORDER BY 
                            ABS(TIMESTAMPDIFF(SECOND, tg.created_on, NOW()))
                            LIMIT 1", [$trip_id]);
                            
                            $geofence = !empty($geofence) ? $geofence[0] : null;
                            $vehid = !empty($geofence->vehicle_id) ? $geofence->vehicle_id : 0;
                            
                            if (isset($geofence->plat) && $geofence->plat != '' && isset($geofence->maplat) && $geofence->maplat != '') {
                                $data['geofencests'] = 'IN';
                                $data['geofencests_latlngs'] = ['lat1'=>$geofence->plat, 'lng1'=>$geofence->plng, 'lat2'=>$geofence->maplat, 'lng2'=>$geofence->maplng, 'unit'=>$unit];
                                // untill distances calcualtion is integrated
                                // $geo = geoFenceDistance($geofence->plat, $geofence->plng, $geofence->maplat, $geofence->maplng, $unit);
                                // if ($geo < 1) {
                                //     $data['geofencests'] = 'IN';
                                //     DB::table('tb_trucks_data')->where('id', $vehid)->update(['geofence_status' => 1]);
                                // } else {
                                //     $data['geofencests'] = 'OUT';
                                //     DB::table('tb_trucks_data')->where('id', $vehid)->update(['geofence_status' => 0]);
                                // }
                            }

                            $geofencecord = DB::select("SELECT tt.id AS trpid,tt.plat,tt.plng,tt.shift_id,tg.routeid,tg.trip_id,tg.vehicle_id,tgp.routeid AS rid,tgp.latitude,tgp.longitude
                            FROM tb_trips AS tt 
                            INNER JOIN tb_geoassign AS tg ON tg.trip_id = tt.id
                            INNER JOIN tb_georoute_pos AS tgp ON tgp.routeid = tg.routeid 
                            WHERE tt.id = ? ORDER BY 
                            ABS(TIMESTAMPDIFF(SECOND, tg.created_on, NOW()))
                            LIMIT 1", [$trip_id]);
                            
                            $geofencecord = !empty($geofencecord) ? $geofencecord[0] : null;
                            $vid = !empty($geofencecord->vehicle_id) ? $geofencecord->vehicle_id : 0;
                            
                            if (isset($geofencecord->plat) && $geofencecord->plat != '' && isset($geofencecord->latitude) && $geofencecord->latitude != '') {
                                $data['geofencests'] = 'IN';
                                $data['geofencests_latlngs'] = ['lat1'=>$geofencecord->plat, 'lng1'=>$geofencecord->plng, 'lat2'=>$geofencecord->latitude, 'lng2'=>$geofencecord->longitude, 'unit'=>$unit];
                                // $geo = geoFenceDistance($geofencecord->plat, $geofencecord->plng, $geofencecord->latitude, $geofencecord->longitude, $unit);
                                // if ($geo < 1) {
                                //     $data['geofencests'] = 'IN';
                                //     DB::table('tb_trucks_data')->where('id', $vid)->update(['geofence_status' => 1]);
                                // } else {
                                //     $data['geofencests'] = 'OUT';
                                //     DB::table('tb_trucks_data')->where('id', $vid)->update(['geofence_status' => 0]);
                                // }
                            }
                            
                            if ($tripId != '') {
                                $data["trip_id"] = $tripId;
                            }
                            $data["pings"] = count($data["records"]);
                        }
                    }
                }
                            
                return response()->json([
                    'status' => 'success',
                    'message' => 'Trip details retrieved successfully.',
                    'data' => $data
                ], 200);
            } catch (Exception $e) {
                Log::error('Error in tripEtnshipment: ' . $e->getMessage());
                return response()->json([
                    'status' => 'error',
                    'message' => 'An error occurred while fetching trip details.',
                    'error' => $e->getMessage()
                ], 500);
            }
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid shift ID provided.',
                'data' => null
            ], 400);
        }

        } catch (\Exception $e) {
            Log::error('Error in tripEtnshipment: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve trip details.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function chkTripinfoByOrdertrip($ord,$curtz){
        $res = array();
        if($ord != ""){
            $sql = "SELECT id,shift_id,driver_id,vehicle_id,convertToClientTZ(stime,'".$curtz."') as stime,convertToClientTZ(etime,'".$curtz."') as etime,status,dlat as latitude,dlng as longitude FROM tb_trips WHERE id='".$ord."' ORDER BY id DESC LIMIT 1";
            $query = DB::select($sql);
            if($query){
                if (count($query) > 0) {
                    $res = $query[0];
                }
            }
        }
        return $res;
    }

    public function generateTWB(GenerateTWBRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get shift_id from validated request
            $shiftId = $request->input('shift_id');
            
            // Get order details by shift ID
            $orderObj = new Order;
            $orderDetails = $orderObj->getOrderIdsByShiftId($shiftId);
            
            // Check if order details exist
            if (empty($orderDetails)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No orders found for this shift.',
                    'data' => null,
                ], 404);
            }
            
            // Check for Xborder Main Trip
            $crossBorderMainTrip = $orderDetails['crossBorderMainTrip'] ?? 0;
            if ($crossBorderMainTrip > 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'TWB cannot be generated for Xborder Main Trip',
                    'data' => null,
                ], 400);
            }
            
            // Check if orders array exists
            if (empty($orderDetails['orders'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No order data found for this shift.',
                    'data' => null,
                ], 404);
            }
            
            // Initialize data arrays
            $orderRowIds = $orderData = $customerIds = [];
            
            // Get trip details
            $tripObj = new Trip;
            $tripData = $tripObj->getTripsDetailsByshiftId($shiftId);
            
            // Process each order
            foreach ($orderDetails['orders'] as $eachOrderId) {
                $orderRowId = $eachOrderId['id'];
                $customerRowId = $eachOrderId['customer_id'];
                $orderRowIds[] = $orderRowId;
                $customerIds[] = $customerRowId;
                $orderData[$orderRowId] = [
                    'id' => $orderRowId,
                    'bookingId' => $eachOrderId['order_id'],
                    'customerRowId' => $customerRowId,
                    'pickupCompany' => $eachOrderId['pickup_company'],
                    'pickupCountry' => $eachOrderId['pickup_country'],
                    'pickupCity' => $eachOrderId['pickup_city'],
                    'pickupPincode' => $eachOrderId['pickup_pincode'],
                    'pickupAddress1' => $eachOrderId['pickup_address1'],
                    'pickupAddress2' => $eachOrderId['pickup_address2'],
                    'deliveryCompany' => $eachOrderId['delivery_company'],
                    'deliveryCountry' => $eachOrderId['delivery_country'],
                    'deliveryCity' => $eachOrderId['delivery_city'],
                    'deliveryPincode' => $eachOrderId['delivery_pincode'],
                    'deliveryAddress1' => $eachOrderId['delivery_address1'],
                    'deliveryAddress2' => $eachOrderId['delivery_address2'],
                    'pickupDateTime' => $eachOrderId['pickup_datetime'],
                    'dropEndTime' => $eachOrderId['drop_endtime'],
                    'createdSource' => $eachOrderId['created_source'],
                    'branchCode' => $eachOrderId['branch_code'],
                    'totalLdm' => 0,
                    'jfr' => ''
                ];
            }
            
            // Check if we have any orders to process
            if (empty($orderRowIds)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No valid orders found to generate TWB.',
                    'data' => null,
                ], 404);
            }
            
            // Process order details
            $orderData = $this->getOrderDetailsTableData($orderRowIds, $orderData);
            $orderData = $this->getOrderCargoDetails($orderRowIds, $orderData);
            $orderData = $this->getCustomerMasterDetails($customerIds, $orderData);
            $orderData = $this->getOrderReferencesWithCodes($orderRowIds, $orderData);
            $orderData = $this->getOrderPartyTypes($orderRowIds, $orderData);
            
            // Get debtor JFR
            $getDebtorJfr = $orderObj->getDebtorJfrFromOrder($orderRowIds);
            foreach ($getDebtorJfr as $eachRev) {
                $orderRowId = $eachRev['order_id'];
                $checkOrder = $orderData[$orderRowId]['id'] ?? 0;
                if ($checkOrder > 0) {
                    $orderData[$orderRowId]['jfr'] = $eachRev['debtor_jfr'];
                }
            }
            
            // Generate TWB data
            $twbData = $this->arrangDataDownloadForTruckWayBill($orderData, $tripData);
            
            return response()->json([
                'status' => 'success',
                'message' => 'Generated TWB data successfully.',
                'data' => $twbData
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate TWB data.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    private function arrangDataDownloadForTruckWayBill(array $orderData, array $tripData): array
    {
        $user = Auth::guard('api')->user();
        $data['branch_code'] = $user->be_value;
        $data['user_id'] = $user->id;

        $userQuery = "SELECT employee_name FROM sx_users WHERE id = ?";
        $userData = DB::select($userQuery, [$data['user_id']]);

        $branchQuery = "SELECT branch_name FROM branch_masters WHERE branch_code = ?";
        $branchDetails = DB::select($branchQuery, [$data['branch_code']]);

        $shipmentId = $tripData['shipmentId'];
        $orderDataCount = count($orderData);
        foreach ($orderData as $orderRowId => $eachOrder) {
            $data[$orderRowId]['salog_tracking_number'] = $eachOrder['salogTrackingNumber'] ?? "";
            $data[$orderRowId]['order_reference'] = $eachOrder['references'] ?? [];
            $data[$orderRowId]['cargo_details'] = $eachOrder['cargo_details'];
            $data[$orderRowId]['user_name'] = $userData[0]->employee_name;

            $barCode = new BarcodeGeneratorPNG();
            $data[$orderRowId]["barcode"] = 'data:image/png;base64,' . base64_encode($barCode->getBarcode($eachOrder['bookingId'], $barCode::TYPE_CODE_128));

            $branchDetails['branch_name'] = $branchDetails[0]->branch_name ?? "";
            $data[$orderRowId]['branch_details'] = $branchDetails;

            $additionalInfo['tracking_number'] = $eachOrder['bookingId'];
            $additionalInfo['created_source'] = $eachOrder['createdSource'];
            $additionalInfo['late_delivery'] = $eachOrder['dropEndTime'];
            $additionalInfo['inco_term'] = $eachOrder['inco_term'];
            $additionalInfo['delivery_term'] = $eachOrder['delivery_term'];
            $additionalInfo['customs_required'] = $eachOrder['customs_required'];
            $additionalInfo['lane_reference'] = $eachOrder['lane_reference'];
            $additionalInfo['ldm'] = $eachOrder['totalLdm'];
            $additionalInfo['debtor_jfr'] = $eachOrder['jfr'];
            $additionalInfo['driver_name'] = $tripData['driverName'];
            $additionalInfo['truck_num'] = $tripData['vehicleNumber'];
            $additionalInfo['trailer_num'] = $tripData['tailerNumber'];
            $additionalInfo['trucktype'] = $tripData['vehicleType'];
            $additionalInfo['departure_date'] = $tripData['departureDate'] ?: $eachOrder['pickupDateTime'];
            // $additionalInfo['branch_officeno'] = $userData['BranchOfficeNumber'];
            $data[$orderRowId]['add_info'] = $additionalInfo;

            $pickup_address['pickup_company'] = $eachOrder['pickupCompany'];
            $pickup_address['pickup_country'] = $eachOrder['pickupCountry'];
            $pickup_address['pickup_city'] = $eachOrder['pickupCity'];
            $pickup_address['pickup_pincode'] = $eachOrder['pickupPincode'];
            $pickup_address['pickup_address1'] = $eachOrder['pickupAddress1'];
            $pickup_address['pickup_address2'] = $eachOrder['pickupAddress2'];
            $data[$orderRowId]['pickup_address'] = $pickup_address;

            $delivery_address['delivery_company'] = $eachOrder['deliveryCompany'];
            $delivery_address['delivery_country'] = $eachOrder['deliveryCountry'];
            $delivery_address['delivery_city'] = $eachOrder['deliveryCity'];
            $delivery_address['delivery_pincode'] = $eachOrder['deliveryPincode'];
            $delivery_address['delivery_address1'] = $eachOrder['deliveryAddress1'];
            $delivery_address['delivery_address2'] = $eachOrder['deliveryAddress2'];
            $data[$orderRowId]['delivery_address'] = $delivery_address;

            $newshipper_address['name'] = $eachOrder['shipperName'] ?? "";
            $newshipper_address['location'] = $eachOrder['shipperLocation'] ?? "";
            $newshipper_address['street'] = $eachOrder['shipperStreet'] ?? "";
            $newshipper_address['state'] = $eachOrder['shipperState'] ?? "";
            $newshipper_address['country'] = $eachOrder['shipperCountry'] ?? "";
            $newshipper_address['pincode'] = $eachOrder['shipperPincode'] ?? "";
            $data[$orderRowId]['newshipper_address'] = $newshipper_address;

            $consignee_address['name'] = $eachOrder['consigneeName'] ?? "";
            $consignee_address['location'] = $eachOrder['consigneeLocation'] ?? "";
            $consignee_address['street'] = $eachOrder['consigneeStreet'] ?? "";
            $consignee_address['state'] = $eachOrder['consigneeState'] ?? "";
            $consignee_address['country'] = $eachOrder['consigneeCountry'] ?? "";
            $consignee_address['pincode'] = $eachOrder['consigneePincode'] ?? "";
            $data[$orderRowId]['consignee_address'] = $consignee_address;

            $noity_address['name'] = $eachOrder['notifyName'] ?? "";
            $noity_address['location_id'] = $eachOrder['notifyLocation'] ?? "";
            $noity_address['address'] = "";
            $noity_address['country'] = $eachOrder['notifyCountry'] ?? "";
            $noity_address['state'] = $eachOrder['notifyState'] ?? "";
            $noity_address['street'] = $eachOrder['notifyStreet'] ?? "";
            $noity_address['pincode'] = $eachOrder['notifyPincode'] ?? "";
            $data[$orderRowId]['noity_address'] = $noity_address;

            $shipper_address['name'] = $eachOrder['customerName'];
            $shipper_address['location'] = $eachOrder['customerLocation'];
            $shipper_address['street'] = $eachOrder['customerStreet'];
            $shipper_address['state'] = $eachOrder['customerState'];
            $shipper_address['country'] = $eachOrder['customerCountry'];
            $shipper_address['pincode'] = $eachOrder['customerPincode'];
            $data[$orderRowId]['shipper_address'] = $shipper_address;
        }
        return $data;
    }

    public function showNearbyVehicles(ShowNearbyVehiclesRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $id = $user->id;
            $org_id = $user->default_org_id;
            
            if (empty($id) || empty($org_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID or organization ID.',
                    'data' => null,
                ], 400);
            }

            // Store request data
            $data["shift_id"] = $request->input('shift_id');
            $data["carrier_id"] = $request->input('carrier_id');
            $data["pickup_latitude"] = $request->input('latitude');
            $data["pickup_longitude"] = $request->input('longitude');
            
            // Query to get vehicles with driver information
            $sql = "SELECT ttd.id, ttd.name, ttd.email, ttd.contact_num, td.id as vehicle_id, td.truck_number, td.latitude, td.longitude 
                    FROM vehicles_drivers AS tvd 
                    LEFT JOIN truck_drivers AS ttd ON ttd.id = tvd.driver_id 
                    LEFT JOIN sx_users AS v ON v.id = ttd.vendor_id 
                    LEFT JOIN trucks_data AS td ON tvd.vehicle_id = td.id 
                    WHERE td.latitude IS NOT NULL AND td.longitude IS NOT NULL 
                    AND ttd.org_id = ? AND ttd.status = 1 
                    AND td.status = '1' AND tvd.status = '1' 
                    GROUP BY ttd.id, td.id 
                    ORDER BY ttd.id DESC";
            
            $vehicles = [];
            $query = DB::select($sql, [$org_id]);
            
            // Process query results
            foreach ($query as $eachRow) {
                $vehicles[] = [
                    'id' => $eachRow->id,
                    'lat' => (float)$eachRow->latitude,
                    'lng' => (float)$eachRow->longitude,
                    'vehicleNumber' => $eachRow->truck_number,
                    'driverName' => $eachRow->name,
                    'driverEmail' => $eachRow->email,
                    'driverPhone' => $eachRow->contact_num,
                    'vehicleId' => $eachRow->vehicle_id
                ];
            }
            
            $data["vehicles"] = $vehicles;

            return response()->json([
                'status' => 'success',
                'message' => 'Nearby vehicles retrieved successfully.',
                'data' => $data
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve nearby vehicles.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function rollBackOrderTripDetailsAndShipmentDelete(RollbackTripShipmentDeleteRequest $request, int $id)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $userId = $user->id;
            $orgId = $user->default_org_id;
            
            if (empty($userId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($orgId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get shift ID from URL parameter
            $shiftId = $id;

            // Check if shipment is already deleted
            $shipmentStatus = DB::table('shipment')->where('id', $shiftId)->value('status');
            if ($shipmentStatus == 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Shipment is already deleted.',
                    'data' => null,
                ], 409);
            }

            // Check if trip is running
            if (DB::table('trips')->where(["shift_id" => $shiftId, "status" => '1'])->exists()) {
                return response()->json([
                    'status' => 'error',
                    'message' => "You can't delete a Shipment while its Trip is RUNNING",
                    'data' => null,
                ], 400);
            }

            // Get orders associated with this shipment
            $getQuery = DB::table('orders')
                ->select("id, shipmentid, order_id, vendor_id, user_id")
                ->where(["shift_id" => $shiftId])
                ->get();
            
            if ($getQuery->count() > 0) {
                $ordersData = [];
                foreach ($getQuery as $row) {
                    $ordersData[$row->id] = [
                        'shipmentid' => $row->shipmentid
                    ];
                }
                $orderIds = array_keys($ordersData);
                
                // Rollback order data
                DB::table('orders')->whereIn("id", $orderIds)->update(["shift_id" => 0, "trip_id" => 0, "trip_sts" => 0, "shipmentid" => null, "status" => 1]);
                
                // Update shipment stops
                DB::table('shiporder_stops')->where("shipment_id", $shiftId)->update(["status" => 0]);
                DB::table('shiporder_stop_sequence')->where("shift_id", $shiftId)->update(["status" => 0]);

                // Get shift vehicle data
                $getShiftData = DB::table('shft_veh')
                    ->select('id, register_number, vehicle_id')
                    ->where("status", 1)
                    ->where("shft_id", $shiftId)
                    ->get();

                $registerNumber = null;
                $shiftVehIds = [];
                if ($getShiftData->count() > 0) {
                    foreach ($getShiftData as $shiftData) {
                        $shiftVehIds[] = $shiftData->id;
                        $registerNumber = $shiftData->register_number;
                    }
                }

                // Update shift vehicles
                DB::table('shft_veh')->where("shft_id", $shiftId)->update(["status" => 0]);
                if (!empty($shiftVehIds)){
                   DB::table('shipment_vehicle_stopsleg')->whereIn("shft_veh_id", $shiftVehIds)->update(["status" => 0]);
                }
                
                // Update shipment status
                DB::table('shipment')->where("id", $shiftId)->update(["status" => 0]);

                // Get shipment data after deletion
                $getShipmentData = DB::table('shipment')
                    ->select('id, deleted_at')
                    ->where("id", $shiftId)
                    ->first();

                // Log activity for each order
                $user_business_type = $user->business_type ?? 'Country Admin';

                foreach ($orderIds as $orderId) {
                    $activityData = [
                        "order_id" => $orderId,
                        "shift_id" => $shiftId,
                        "action_name" => "TRIP DELETE",
                        "description" => "This " . $ordersData[$orderId]['shipmentid'] . " of trip with vehicle number: " . $registerNumber . " was deleted by user of role: " . $user_business_type,
                        "user_id" => $user->id,
                        "user_type" => $user_business_type
                    ];
                    DB::table("order_activities")->insert($activityData);
                }
                
                return response()->json([
                    'status' => 'success',
                    'message' => "Rollback order trip details and shipment deleted successfully.",
                    'data' => [
                        'shift_id' => $shiftId,
                        'deleted_at' => $getShipmentData->deleted_at
                    ]
                ], 200);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No orders found for this shipment.',
                    'data' => null,
                ], 404);
            }

        } catch (\Exception $e) {
            Log::error('Error in rollBackOrderTripDetailsAndShipmentDelete: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to rollback and delete shipment.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function shipmentDelete($id = null)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $userId = $user->id;
            $orgId = $user->default_org_id;
            
            if (empty($userId) || empty($orgId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID or organization ID.',
                    'data' => null,
                ], 400);
            }

            // Validate shipment ID
            if ($id == null || empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Given Shipment ID is invalid',
                    'data' => null,
                ], 400);
            }

            // Check if shipment exists
            $shipmentExists = DB::table('shipment')->where('id', $id)->exists();
            if (!$shipmentExists) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Shipment not found.',
                    'data' => null,
                ], 404);
            }

            // Check if shipment is already deleted
            $shipmentStatus = DB::table('shipment')->where('id', $id)->value('status');
            if ($shipmentStatus == 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Shipment is already deleted.',
                    'data' => null,
                ], 409);
            }

            // Check if trip is running
            if (DB::table('trips')->where(["shift_id" => $id, "status" => '1'])->count() > 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => "You can't delete a Shipment while its Trip is RUNNING",
                    'data' => null,
                ], 400);
            }

            // Delete shipment and shift vehicle
            DB::table('shipment')->where("id", $id)->update(["status" => '0']);
            DB::table('shft_veh')->where("shft_id", $id)->update(["status" => '0']);

            // Get shipment data after deletion
            $getShipmentData = DB::table('shipment')
                ->select('id, deleted_at')
                ->where("id", $id)
                ->first();

            return response()->json([
                'status' => 'success',
                'message' => 'Shipment deleted successfully.',
                'data' => [
                    'shift_id' => $id,
                    'deleted_at' => $getShipmentData->deleted_at
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete shipment.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
