<?php

namespace App\Http\Controllers;

use App\Models\RouteTemplate;
use App\Models\RouteTemplateLeg;
use App\Models\WaypointRouteTemplate;
use App\Models\ServiceMaster;
use App\Models\OrderType;
use App\Models\Shipment;
use App\Models\XborderCountry;
use App\Models\Customer;
use App\Models\SxPartyMembers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\TripTemplateIndexRequest;
use App\Http\Requests\TripTemplateShowRequest;
use App\Http\Requests\TripTemplateStoreRequest;
use App\Http\Requests\TripTemplateDestroyRequest;
use App\Services\PartyManagementService;

class TripTemplateController extends Controller
{
    protected $routeTemplateModel;
    protected $routeTemplateLegModel;
    protected $waypointRouteTemplateModel;
    protected $serviceMasterModel;
    protected $customerModel;
    protected $orderTypeModel;
    protected $shiftModel;
    protected $xborderCountryModel;
    protected $partyModel;
    protected $partyManagementService;

    public function __construct(PartyManagementService $partyManagementService)
    {
        $this->routeTemplateModel = new RouteTemplate();
        $this->routeTemplateLegModel = new RouteTemplateLeg();
        $this->waypointRouteTemplateModel = new WaypointRouteTemplate();
        $this->serviceMasterModel = new ServiceMaster();
        $this->orderTypeModel = new OrderType();
        $this->shiftModel = new Shipment();
        $this->xborderCountryModel = new XborderCountry();
        $this->partyModel = new SxPartyMembers();
        $this->partyManagementService = $partyManagementService;
    }

    public function triptemplateindex(TripTemplateIndexRequest $request)
    {
        try {
            // Validate authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized access. Please login.',
                    'data' => null,
                ], 401);
            }

            // Validate user data
            $userId = $user->id;
            $userOrgId = $user->default_org_id;
            
            if (empty($userId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid user ID.',
                    'data' => null,
                ], 400);
            }

            if (empty($userOrgId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Get org_id from request, use user's default org_id if not provided
            $org_id = $request->input('org_id') ?? $userOrgId;
            $be_value = $request->input('be_value') ?? null;

        // Initialize where condition
        $where = ['org_id' => $org_id];

        // Adjust conditions based on org_id and be_value
        if ($org_id !== '44' && !empty($be_value)) {
            $where['be_value'] = $be_value;
        }

        // Handle search parameters
        $search_params = $request->all();

        // General search
        if (!empty($search_params['searchsubmit']) && $search_params['searchsubmit'] === 'Search') {
            if (!empty($search_params['template_id'])) {
                $where['template_id'] = $search_params['template_id'];
            }
            if (!empty($search_params['template_name'])) {
                $where['template_name'] = $search_params['template_name'];
            }
            if (!empty($search_params['be_value'])) {
                $where['be_value'] = $search_params['be_value'];
                if ($be_value && $where['be_value'] !== $be_value) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized access to this branch',
                    ], 403);
                }
            }
        }
        // Advanced search
        elseif (!empty($search_params['searchsubmita']) && $search_params['searchsubmita'] === 'Search') {
            if (!empty($search_params['template_id'])) {
                $where['template_id'] = $search_params['template_id'];
            }
            if (isset($search_params['active']) && $search_params['active'] !== '') {
                $where['active'] = $search_params['active'];
            }
            if (!empty($search_params['service'])) {
                $where['service'] = $search_params['service'];
            }
            if (!empty($search_params['customer_id'])) {
                $where['customer_id'] = $search_params['customer_id'];
            }
        }

        // Fetch records using Eloquent with all required columns
        $query = RouteTemplate::select([
            'id',
            'template_id',
            'template_name',
            'description',
            'product',
            'service',
            'customer_id',
            'order_type',
            'carrier_type',
            'shipment_type',
            'min_distance',
            'max_distance',
            'min_weight',
            'max_weight',
            'min_volume',
            'max_volume',
            'active',
            'org_id',
            'be_value'
        ]);
        
        // Apply where conditions
        foreach ($where as $key => $value) {
            $query->where($key, $value);
        }
        
        // Add pagination
        $perPage = (int) ($request->input('per_page', 15));
        $page = max(1, (int) ($request->input('page', 1)));

        $route_templates = $query->orderBy('id')->paginate($perPage, ['*'], 'page', $page);
        $total = $route_templates->total();
        
        // Debug: Log query and results
        Log::info('TripTemplate Index Query', [
            'where_conditions' => $where,
            'count' => $route_templates->count(),
            'first_record' => $route_templates->first()
        ]);

        // Check if records are empty
        // if ($route_templates->isEmpty()) {
        //     return response()->json([
        //         'status' => 'success',
        //         'data' => [],
        //         'message' => 'No records found',
        //     ], 200);
        // }

        // Map records to output format
        $ids = $route_templates->reduce(function ($carry, $template) {
            $carry['services'][] = $template->service;
            $carry['customers'][] = $template->customer_id;
            $carry['orderTypes'][] = $template->order_type;
            return $carry;
        }, ['services' => [], 'customers' => [], 'orderTypes' => []]);

        $data = [
            'templates' => $route_templates,
            'servicesData' => [],
            'customersData' => [],
            'orderTypesData' => [],
            'postData' => $search_params,
        ];

        // Process services data
        if (!empty($ids['services'])) {
            $servicesData = ServiceMaster::whereIn('service_id', array_unique($ids['services']))
                ->pluck('name', 'service_id')
                ->map(function ($name, $service_id) {
                    return "{$service_id}-{$name}";
                })->toArray();
            $data['servicesData'] = $servicesData;
        }

        // Process customers data
        if (!empty($ids['customers'])) {
            $customersData = Customer::whereIn('id', array_unique($ids['customers']))
                ->pluck('name', 'id')
                ->toArray();
            $data['customersData'] = $customersData;
        }

        // Process order types data
        if (!empty($ids['orderTypes'])) {
            $orderTypesData = OrderType::whereIn('ordtype_code', array_unique($ids['orderTypes']))
                ->pluck('type_name', 'ordtype_code')
                ->toArray();
            $data['orderTypesData'] = $orderTypesData;
        }

        $mappedTemplates = $route_templates->map(function ($row) {
            return [
                'id' => $row->id,
                'template_id' => $row->template_id,
                'template_name' => $row->template_name,
                'active' => $row->active,
                'description' => $row->description ?? '',
                'product' => $row->product ?? '',
                'service' => $row->service ?? '',
                'customer_id' => $row->customer_id ?? 0,
                'order_type' => $row->order_type ?? '',
                'carrier_type' => $row->carrier_type ?? '',
                'shipment_type' => $row->shipment_type ?? '',
                'minimum_distance' => $row->min_distance ?? 0,
                'maximum_distance' => $row->max_distance ?? 0,
                'minimum_weight' => $row->min_weight ?? 0,
                'maximum_weight' => $row->max_weight ?? 0,
                'minimum_volume' => $row->min_volume ?? 0,
                'maximum_volume' => $row->max_volume ?? 0,
                'org_id' => $row->org_id,
                'be_value' => $row->be_value,
            ];
        });

        $responseData = [
            'status' => 'true',
            'message' => 'Trip templates retrieved successfully.',
            'data' => [
                'page' => $route_templates->currentPage(),
                'perPage' => $route_templates->perPage(),
                'total' => $total,
                'records' => $mappedTemplates,
            ],
        ];

            // Return JSON response for API
            return response()->json($responseData, 200);
        } catch (\Exception $e) {
            Log::error('Error in triptemplateindex: ' . $e->getMessage());
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve trip templates.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function addTemplate()
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = request()->input('org_id') ?? '44'; // Default for testing
        $be_value = request()->input('be_value') ?? null;

        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $data = [
            'org_id' => $org_id,
            'be_value' => $be_value,
            'page_title' => 'Add Trip Template',
            'sub_title' => 'Add New Trip Template',
        ];

        // Check if it's an API request
        if (request()->expectsJson()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Add template form data retrieved successfully',
                'data' => $data,
            ], 200);
        }

    }

    public function store(TripTemplateStoreRequest $request)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $userOrgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($userOrgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Check for duplicate template names within organization
            $data = $request->validated();
            $existingTemplate = RouteTemplate::where([
                'template_name' => $data['templateName'],
                'org_id' => $userOrgId,
                'status' => 1
            ])->first();

            if ($existingTemplate) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Trip template already exists with this name.',
                    'data' => null,
                    'errors' => ['templateName' => 'Trip template name already exists in your organization.'],
                ], 409);
            }

            // Get org_id and user_id from authenticated user
            $org_id = $userOrgId;
            $be_value = $user->be_value ?? null;
            $user_id = $user->id;

            $post = $request->all();
            $activeTemplate = $data['activeTemplate'] ?? 0;
            $templateRowId = isset($post['templateRowId']) ? (int)$post['templateRowId'] : 0;

            $timezone = config('app.timezone', 'Asia/Kolkata');
            $currentDate = now()->timezone($timezone)->format('Y-m-d H:i:s');

            $templateData = [
                'template_name' => $data['templateName'],
                'active' => $activeTemplate ? 1 : 0,
                'description' => $data['templateDescription'] ?? '',
                'product' => $data['product'] ?? '',
                'service' => $data['service'] ?? '',
                'order_type' => isset($data['orderType']) && $data['orderType'] > 0 ? (string)$data['orderType'] : '',
                'carrier_type' => $data['carrierType'] ?? '',
                'shipment_type' => $data['shipmentType'] ?? '',
                'min_distance' => isset($data['minimumDistance']) && $data['minimumDistance'] > 0 ? (float)$data['minimumDistance'] : 0.00,
                'mindistance_units' => $post['minDistanceUom'] ?? 'KM',
                'max_distance' => isset($data['maximumDistance']) && $data['maximumDistance'] > 0 ? (float)$data['maximumDistance'] : 0.00,
                'maxdistance_units' => $post['maxDistanceUom'] ?? 'KM',
                'min_volume' => isset($data['minimumVolume']) && $data['minimumVolume'] > 0 ? (float)$data['minimumVolume'] : 0.00,
                'minvolume_units' => $post['minVolumeUom'] ?? 'CBM',
                'max_volume' => isset($data['maximumVolume']) && $data['maximumVolume'] > 0 ? (float)$data['maximumVolume'] : 0.00,
                'maxvolume_units' => $post['maxVolumeUom'] ?? 'CBM',
                'min_weight' => isset($data['minimumWeight']) && $data['minimumWeight'] > 0 ? (float)$data['minimumWeight'] : 0.00,
                'minweight_units' => $post['minWeightUom'] ?? 'KG',
                'max_weight' => isset($data['maximumWeight']) && $data['maximumWeight'] > 0 ? (float)$data['maximumWeight'] : 0.00,
                'maxweight_units' => $post['maxWeightUom'] ?? 'KG',
                'container_number' => $data['container_number'] ?? null,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'created_on' => $currentDate,
            ];

            // Handle create vs update
            if ($templateRowId > 0) {
                // Update existing template
                $templateData['template_id'] = $post['templateId'];
                $this->routeTemplateModel->where('id', $templateRowId)->update($templateData);
                $finalTemplateRowId = $templateRowId;
            } else {
                // Create new template
                $templateData['template_id'] = $this->generateTripTemplateId();
                
                // Get next ID and update sequence properly
                $maxId = RouteTemplate::max('id') ?? 0;
                $nextId = $maxId + 1;
                
                // Set sequence to next ID (using is_called=false so nextval() will return this exact value)
                // This ensures sequential IDs without gaps
                DB::statement("SELECT setval('route_templates_id_seq', $nextId, false)");
                
                $templateData['id'] = $nextId;
                
                Log::info('Creating route template with data:', $templateData);
                $template = $this->routeTemplateModel->create($templateData);
                $finalTemplateRowId = $template->id;
                
                Log::info('Route template created successfully with ID: ' . $finalTemplateRowId);
            }

            // Save legs if provided (preserving existing business logic)
            if ($finalTemplateRowId > 0) {
                $post['templateRowId'] = $finalTemplateRowId;
                $legIds = $this->saveLegs($post, $templateData['template_id']);
                if (!empty($legIds)) {
                    $this->routeTemplateLegModel->whereIn('id', $legIds)->update(['routetemplate_id' => $finalTemplateRowId]);
                }
                
                return response()->json([
                    'status' => 'true',
                    'message' => 'Trip template saved successfully.',
                    'data' => [
                        'id' => $finalTemplateRowId,
                        'template_id' => $templateData['template_id'],
                        'template_name' => $templateData['template_name'],
                    ],
                ], 200);
            }

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to save template.',
                'data' => null,
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error in store triptemplate: ' . $e->getMessage());
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to save trip template.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function generateTripTemplateId()
    {
        $currentYear = now()->format('y');
        $currentWeek = now()->format('W');

        $latestTemplate = $this->routeTemplateModel->orderBy('template_id', 'desc')->first();

        if (!$latestTemplate) {
            $sequence = 1;
        } else {
            $previousTemplateId = $latestTemplate->template_id;
            $previousWeekNumber = substr($previousTemplateId, 4, 2);
            $previousSequence = ltrim(substr($previousTemplateId, 6), '0');
            $sequence = ($previousWeekNumber < $currentWeek) ? 1 : ((int) $previousSequence + 1);
        }

        $templateId = str_pad($sequence, 4, '0', STR_PAD_LEFT);
        return "TT{$currentYear}{$currentWeek}{$templateId}";
    }

    private function saveLegs(array $data, string $shipmentId): array
    {
        $legRowIds = [];
        $templateRowId = $data['templateRowId'] ?? 0;

        // Set all waypoints for this template to status 0
        $this->waypointRouteTemplateModel->where('template_id', $templateRowId)->update(['status' => 0]);

        $timezone = config('app.timezone', 'Asia/Kolkata');
        $currentDate = now()->timezone($timezone)->format('Y-m-d H:i:s');

        for ($i = 0; $i < 6; $i++) {
            $j = $i + 1;
            $originRowId = $data['originRowId' . $i] ?? 0;
            $destinationRowId = $data['destinationRowId' . $i] ?? 0;

            if (!in_array('0', [$originRowId, $destinationRowId])) {
                $legId = $shipmentId . '-' . $j;
                $legRowId = $data['legRowId' . $i] ?? 0;
                $originLocation = $data['originLocation' . $i] ?? '';
                $destinationLocation = $data['destinationLocation' . $i] ?? '';
                $carrier = (isset($data['carrier' . $i]) && $data['carrier' . $i] > 0) ? $data['carrier' . $i] : 0;
                $vehicleType = (isset($data['vehicleType' . $i]) && !empty($data['vehicleType' . $i])) ? $data['vehicleType' . $i] : 0;
                $vesselNumber = (isset($data['vessel_number' . $i]) && !empty($data['vessel_number' . $i])) ? $data['vessel_number' . $i] : null;
                $modeOfTransport = (isset($data['modeOfTransport' . $i]) && !empty($data['modeOfTransport' . $i])) ? $data['modeOfTransport' . $i] : '';
                $vehicle = (isset($data['vehicle' . $i]) && $data['vehicle' . $i] > 0) ? $data['vehicle' . $i] : 0;
                $driver = (isset($data['driver' . $i]) && $data['driver' . $i] > 0) ? $data['driver' . $i] : 0;

                $legData = [
                    'routetemplate_id' => $templateRowId,
                    'leg_id' => $legId,
                    'origin_id' => $originRowId,
                    'origin_location' => $originLocation,
                    'destination_id' => $destinationRowId,
                    'destination_location' => $destinationLocation,
                    'carrier_id' => $carrier,
                    'transport_mode' => $modeOfTransport,
                    'vehicle_type' => $vehicleType,
                    'vehicle_id' => $vehicle,
                    'driver_id' => $driver,
                    'status' => 1,
                    'vessel_number' => $vesselNumber,
                ];

                if ($legRowId > 0) {
                    $legRowIds[] = $legRowId;
                    $this->routeTemplateLegModel->where('id', $legRowId)->update($legData);
                } else {
                    $legData['created_on'] = $currentDate;
                    $newLegRowId = $this->routeTemplateLegModel->create($legData)->id;
                    $legRowIds[] = $newLegRowId;
                    $legRowId = $newLegRowId;
                }

                $selectedWaypoints = $data["selectedWaypoints_leg{$i}"] ?? [];
                if (!empty($selectedWaypoints)) {
                    foreach ($selectedWaypoints as $waypointCode) {
                        $waypointData = [
                            'template_id' => $templateRowId,
                            'party_id' => $waypointCode,
                            'leg_id' => $legId,
                            'leg_row_id' => $legRowId,
                            'created_at' => $currentDate,
                            'status' => 1,
                        ];

                        $existingWaypoint = $this->waypointRouteTemplateModel
                            ->where('leg_row_id', $legRowId)
                            ->where('party_id', $waypointCode)
                            ->first();

                        if ($existingWaypoint) {
                            $existingWaypoint->update([
                                'status' => 1,
                                'updated_at' => $currentDate,
                            ]);
                        } else {
                            $this->waypointRouteTemplateModel->create($waypointData);
                        }
                    }
                    Log::error("Stored waypoints for template ID {$templateRowId}, leg {$legId}: " . implode(', ', $selectedWaypoints));
                }
            }
        }

        return $legRowIds;
    }

    public function show(TripTemplateShowRequest $request, $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Validate user ID
            $userOrgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($userOrgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Find template and verify organization access
            $template = RouteTemplate::where('id', $id)
                ->where('org_id', $userOrgId)
                ->first();

            if (!$template) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Trip template not found.',
                    'data' => null,
                    'errors' => [],
                ], 404);
            }

            // Get legs data for this template
            $legs = RouteTemplateLeg::where('routetemplate_id', $id)
                ->where('status', 1)
                ->orderBy('leg_id')
                ->get();

            // Map template data to response format
            $data = [
                'id' => $template->id,
                'template_id' => $template->template_id,
                'template_name' => $template->template_name,
                'active' => $template->active,
                'description' => $template->description ?? '',
                'product' => $template->product ?? '',
                'service' => $template->service ?? '',
                'customer_id' => $template->customer_id ?? 0,
                'order_type' => $template->order_type ?? '',
                'carrier_type' => $template->carrier_type ?? '',
                'shipment_type' => $template->shipment_type ?? '',
                'minimum_distance' => $template->min_distance ?? 0,
                'maximum_distance' => $template->max_distance ?? 0,
                'minimum_weight' => $template->min_weight ?? 0,
                'maximum_weight' => $template->max_weight ?? 0,
                'minimum_volume' => $template->min_volume ?? 0,
                'maximum_volume' => $template->max_volume ?? 0,
                'org_id' => $template->org_id,
                'be_value' => $template->be_value,
                'legs' => $legs->map(function ($leg) {
                    return [
                        'id' => $leg->id,
                        'leg_id' => $leg->leg_id,
                        'origin_id' => $leg->origin_id,
                        'origin_location' => $leg->origin_location,
                        'destination_id' => $leg->destination_id,
                        'destination_location' => $leg->destination_location,
                        'carrier_id' => $leg->carrier_id,
                        'transport_mode' => $leg->transport_mode,
                        'vehicle_type' => $leg->vehicle_type,
                        'vehicle_id' => $leg->vehicle_id,
                        'driver_id' => $leg->driver_id,
                        'vessel_number' => $leg->vessel_number,
                    ];
                }),
            ];

            return response()->json([
                'status' => 'true',
                'message' => 'Trip template retrieved successfully.',
                'data' => $data,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in show triptemplate: ' . $e->getMessage());
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to retrieve trip template.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function editTemplate($id)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Unauthorized access',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = request()->input('org_id') ?? '44'; // Default for testing
        $be_value = request()->input('be_value') ?? null;

        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid org_id',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        if ($id == 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid template ID',
                'data' => [],
            ], 400);
        }

        // First, let's check if any templates exist in the database
        $allTemplates = RouteTemplate::all();

        // Get template details using RouteTemplate model - remove active condition to see all templates
        $templateDetails = RouteTemplate::where('id', $id)->first();

        // If still not found, try without any conditions
        if (!$templateDetails) {
            $templateDetails = RouteTemplate::first();
        }

        // Check if template exists before accessing properties
        if (!$templateDetails) {
            return response()->json([
                'status' => 'success',
                'message' => 'No template found in database',
                'data' => [],
                'debug' => [
                    'requested_id' => $id,
                    'total_templates_in_db' => $allTemplates->count(),
                    'available_template_ids' => $allTemplates->pluck('id')->toArray(),
                    'available_template_names' => $allTemplates->pluck('template_name')->toArray(),
                ]
            ], 200);
        }

        // Return only actual database data
        $data = [
            'templateRowId' => $id,
            'templateId' => $templateDetails->template_id,
            'templateName' => $templateDetails->template_name,
            'activeTemplate' => $templateDetails->active,
            'templateDescription' => $templateDetails->description,
            'product' => $templateDetails->product,
            'service' => $templateDetails->service,
            'orderType' => $templateDetails->order_type,
            'carrierType' => $templateDetails->carrier_type,
            'shipmentType' => $templateDetails->shipment_type,
            'minimumDistance' => $templateDetails->min_distance,
            'minDistanceUom' => $templateDetails->mindistance_units,
            'maximumDistance' => $templateDetails->max_distance,
            'maxDistanceUom' => $templateDetails->maxdistance_units,
            'minimumVolume' => $templateDetails->min_volume,
            'minVolumeUom' => $templateDetails->minvolume_units,
            'maximumVolume' => $templateDetails->max_volume,
            'maxVolumeUom' => $templateDetails->maxvolume_units,
            'minimumWeight' => $templateDetails->min_weight,
            'minWeightUom' => $templateDetails->minweight_units,
            'maximumWeight' => $templateDetails->max_weight,
            'maxWeightUom' => $templateDetails->maxweight_units,
            'container_number' => $templateDetails->container_number,
            'org_id' => $templateDetails->org_id,
            'be_value' => $templateDetails->be_value,
            'page_title' => 'Edit Trip Template',
            'sub_title' => 'Edit Trip Template',
        ];

        // Check if it's an API request
        if (request()->expectsJson()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Trip template data retrieved successfully from database for editing',
                'data' => $data,
                'debug' => [
                    'found_template_id' => $templateDetails->id,
                    'found_template_name' => $templateDetails->template_name,
                    'total_templates_in_db' => $allTemplates->count(),
                ]
            ], 200);
        }

        // For web requests, return view
        return view('tripstemplate.edit', $data);
    }

    public function destroy(TripTemplateDestroyRequest $request, int $id)
    {
        try {
            // Verify authentication
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Unauthorized access.',
                    'data' => null,
                    'errors' => [],
                ], 401);
            }

            // Get organization ID
            $userOrgId = $user->default_org_id ?? $user->org_id ?? 0;
            if (empty($userOrgId)) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Invalid organization ID.',
                    'data' => null,
                ], 400);
            }

            // Check if template exists and belongs to user's organization
            $template = RouteTemplate::where('id', $id)
                ->where('org_id', $userOrgId)
                ->first();

            if (!$template) {
                return response()->json([
                    'status' => 'false',
                    'message' => 'Template not found or access denied.',
                    'data' => null,
                    'errors' => [],
                ], 404);
            }

            // Soft delete by setting active to 0 (preserving existing business logic)
            $updated = RouteTemplate::where('id', $id)
                ->where('org_id', $userOrgId)
                ->update(['active' => 0]);

            if ($updated) {
                Log::info('Trip template soft deleted', [
                    'template_id' => $id,
                    'org_id' => $userOrgId,
                    'user_id' => $user->id,
                ]);

                return response()->json([
                    'status' => 'true',
                    'message' => 'Trip template deleted successfully.',
                    'data' => [
                        'deleted_template_id' => $id,
                        'deletion_method' => 'soft_delete'
                    ],
                ], 200);
            }

            return response()->json([
                'status' => 'false',
                'message' => 'Failed to delete trip template.',
                'data' => null,
            ], 500);

        } catch (\Exception $e) {
            Log::error('Error in destroy method: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'false',
                'message' => 'Failed to delete trip template.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateTripWithTripsTemplate(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        $org_id = $user->org_id ?? null;
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $timezone = config('app.timezone', 'Asia/Kolkata');
        $currentDate = now()->timezone($timezone)->format('Y-m-d H:i:s');

        $templateId = $request->input('templateId');
        $shiftId = $request->input('shiftId');
        $shipmentId = $request->input('shipmentId');

        if (empty($shiftId) || empty($templateId)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => [
                    'shiftId' => 'The shiftId field is required.',
                    'templateId' => 'The templateId field is required.',
                ],
            ], 422);
        }

        $getTemplateDetails = $this->routeTemplateModel->where('template_id', $templateId)->first();
        if (!$getTemplateDetails) {
            return response()->json([
                'status' => 'error',
                'message' => 'Trip template not found',
                'data' => [],
            ], 404);
        }

        $getTemplateLegsData = $this->routeTemplateLegModel->where('routetemplate_id', $getTemplateDetails->id)->get();
        $daysCount = $getTemplateLegsData->count();

        $this->updateNormalTripAsCrossBorderTrip($getTemplateLegsData->toArray(), $shiftId, $getTemplateDetails->toArray());

        $saveLegs = $this->tripCreateFromOrdersModel->saveTripLegsForShiftId([
            'mainShiftId' => $shiftId,
            'customerId' => $getTemplateDetails->customer_id ?? 0,
            'shiftCreateDate' => $currentDate,
            'shipmentId' => $shipmentId,
            'legsCount' => $daysCount,
            'carrierType' => $getTemplateDetails->carrier_type ?? '',
            'shipmentType' => $getTemplateDetails->shipment_type ?? '',
            'date' => $currentDate,
        ], $getTemplateLegsData->toArray(), $templateId);

        if ($saveLegs) {
            return response()->json([
                'status' => 'success',
                'message' => 'Trip updated successfully with template',
                'data' => [
                    'shiftId' => $shiftId,
                    'templateId' => $getTemplateDetails->template_id,
                    'templateName' => $getTemplateDetails->template_name,
                ],
                'debug' => [
                    'template_found' => true,
                    'template_name' => $getTemplateDetails->template_name,
                    'legs_processed' => $daysCount,
                ]
            ], 200);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Failed to update trip with template',
            'data' => [],
            'debug' => [
                'template_found' => true,
                'template_name' => $getTemplateDetails->template_name,
                'legs_processed' => $daysCount,
            ]
        ], 422);
    }

    private function updateNormalTripAsCrossBorderTrip(array $legsData, $shiftId, array $tripsData)
    {
        $shipmentOriginRowId = 0;
        $shipmentDestinationRowId = 0;

        foreach ($legsData as $index => $leg) {
            $legRowId = $leg['id'] ?? 0;
            if ($legRowId > 0) {
                if ($index === 0) {
                    $shipmentOriginRowId = $leg['origin_id'];
                }
                $shipmentDestinationRowId = $leg['destination_id'];
            }
        }

        if ($shipmentOriginRowId === 0 || $shipmentDestinationRowId === 0) {
            Log::error('Invalid origin or destination ID for shiftId: ' . $shiftId);
            return;
        }

        $originData = $this->partyModel->find($shipmentOriginRowId);
        $destinationData = $this->partyModel->find($shipmentDestinationRowId);

        if (!$originData || !$destinationData) {
            Log::error('Party master data not found for shiftId: ' . $shiftId);
            return;
        }

        $updateData = [
            'carrier_type' => $tripsData['carrier_type'],
            'border_type' => $tripsData['shipment_type'],
            'origin_id' => $originData->code,
            'destination_id' => $destinationData->code,
            'splace' => $originData->city,
            'eplace' => $destinationData->city,
            'scity' => $originData->city,
            'dcity' => $destinationData->city,
            'slat' => $originData->latitude,
            'slng' => $originData->longitude,
            'elat' => $destinationData->latitude,
            'elng' => $destinationData->longitude,
            'updated_on' => now()->timezone(config('app.timezone', 'Asia/Kolkata'))->format('Y-m-d H:i:s'),
        ];

        $updated = $this->shiftModel->where('id', $shiftId)->update($updateData);
        if (!$updated) {
            Log::error('Failed to update shift data for shiftId: ' . $shiftId);
        }
    }

    public function getMasterDataForTripTemplate(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = $request->input('org_id') ?? 'RUKN'; // Default for testing
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'The org_id field is required.',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $code = $request->input('code');
        if (empty($code)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Code is required',
                'data' => [],
            ], 400);
        }

        $getCrossBorderCompanies = $this->xborderCountryModel->where('org_id', $org_id)->first();

        if (!$getCrossBorderCompanies) {
            $crossBorderCompanyCodes = [$org_id];
        } else {
            $countriesString = $getCrossBorderCompanies->xborder_code ?? '';
            $crossBorderCompanyCodes = !empty($countriesString) ? explode(',', $countriesString) : [];
            $crossBorderCompanyCodes[] = $org_id;
        }

        $partyDetails = $this->partyModel->whereIn('org_id', $crossBorderCompanyCodes)
            ->where('code', $code)
            ->where('party_type', $request->input('partyType', 'origin'))
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $partyDetails,
        ], 200);
    }

    public function getWaypointParties(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = $request->input('org_id') ?? 'RUKN'; // Default for testing
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'The org_id field is required.',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $legRowId = $request->input('legRowId') && $request->input('legRowId') > 0 ? (int) $request->input('legRowId') : 0;

        $getPartyMasterData = $this->partyModel->where('org_id', $org_id)->get();

        $existingWaypoints = [];
        if ($legRowId > 0) {
            $waypoints = $this->waypointRouteTemplateModel->where('leg_row_id', $legRowId)->pluck('party_id')->toArray();
            $existingWaypoints = $waypoints;
        }

        if ($getPartyMasterData->isNotEmpty()) {
            $parties = $getPartyMasterData->map(function ($eachLine) use ($existingWaypoints) {
                $isChecked = in_array($eachLine->id, $existingWaypoints) ? ' checked' : '';
                $checkBox = "<input type='checkbox' name='waypointParties[]' id='waypoint_{$eachLine->id}' class='masterlist' value='{$eachLine->id}'{$isChecked}>";
                return [
                    'check' => $checkBox,
                    'id' => $eachLine->code,
                    'name' => $eachLine->name,
                    'email' => $eachLine->code, // As per original, using code as email
                    'mobile' => $eachLine->location_id,
                    'country' => $eachLine->country,
                    'state' => $eachLine->state,
                    'street' => $eachLine->street,
                    'city' => $eachLine->pincode, // As per original, using pincode as city
                    'org_id' => $eachLine->org_id,
                    'be_value' => $eachLine->be_value,
                ];
            })->toArray();

            return response()->json([
                'status' => 'success',
                'data' => $parties,
            ], 200);
        }

        return response()->json([
            'status' => 'success',
            'data' => [],
            'message' => 'No parties found',
        ], 200);
    }
}

?>