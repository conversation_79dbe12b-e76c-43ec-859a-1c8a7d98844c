<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Get Shipments Request Validation
 * 
 * This request class handles validation for the visibility/shipments API endpoint.
 * 
 * Query Parameters:
 * - page: Optional integer, min: 1, max: 10,000 (pagination)
 * - per_page: Optional integer, min: 1, max: 1,000 (pagination)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns shipments data with pagination
 * - Includes shipment details, stops, and related information
 * 
 * @package App\Http\Requests
 */
class GetShipmentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => [
                'nullable',
                'integer',
                'min:1',
                'max:10000'
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'page.integer' => 'Page must be a valid integer.',
            'page.min' => 'Page must be at least 1.',
            'page.max' => 'Page cannot exceed 10,000.',
            'per_page.integer' => 'Per page must be a valid integer.',
            'per_page.min' => 'Per page must be at least 1.',
            'per_page.max' => 'Per page cannot exceed 1,000.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'page' => 'Page',
            'per_page' => 'Per Page',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
            // For example, checking user permissions or organization-specific rules
        });
    }
}
