<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Get History and Documents Request Validation
 * 
 * This request class handles validation for the visibility/historyanddocuments API endpoint.
 * 
 * Request Parameters:
 * - shift_id: Required integer, min: 1 (shipment/shift ID)
 * - stop_id: Optional integer, min: 1 (stop ID for specific stop details)
 * - pickup_id: Optional integer, min: 1 (pickup sequence ID)
 * - order_id: Optional integer, min: 1 (order ID for specific order details)
 * - report_id: Optional integer, min: 0, max: 1 (report type flag)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns shift history and ePOD documents
 * - Includes driver information, trip details, and status updates
 * 
 * @package App\Http\Requests
 */
class GetHistoryAndDocumentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shift_id' => [
                'required',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'stop_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'pickup_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'order_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'report_id' => [
                'nullable',
                'integer',
                'min:0',
                'max:1'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'shift_id.required' => 'Shift ID is required.',
            'shift_id.integer' => 'Shift ID must be a valid integer.',
            'shift_id.min' => 'Shift ID must be at least 1.',
            'shift_id.max' => 'Shift ID cannot exceed 999,999,999.',
            
            'stop_id.integer' => 'Stop ID must be a valid integer.',
            'stop_id.min' => 'Stop ID must be at least 1.',
            'stop_id.max' => 'Stop ID cannot exceed 999,999,999.',
            
            'pickup_id.integer' => 'Pickup ID must be a valid integer.',
            'pickup_id.min' => 'Pickup ID must be at least 1.',
            'pickup_id.max' => 'Pickup ID cannot exceed 999,999,999.',
            
            'order_id.integer' => 'Order ID must be a valid integer.',
            'order_id.min' => 'Order ID must be at least 1.',
            'order_id.max' => 'Order ID cannot exceed 999,999,999.',
            
            'report_id.integer' => 'Report ID must be a valid integer.',
            'report_id.min' => 'Report ID must be 0 or 1.',
            'report_id.max' => 'Report ID must be 0 or 1.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'shift_id' => 'Shift ID',
            'stop_id' => 'Stop ID',
            'pickup_id' => 'Pickup ID',
            'order_id' => 'Order ID',
            'report_id' => 'Report ID',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
            // For example, checking if shift_id exists in shipment table
            // or validating business-specific rules
        });
    }
}
