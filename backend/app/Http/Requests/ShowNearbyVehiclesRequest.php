<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Show Nearby Vehicles Request Validation
 * 
 * This request class handles validation for the visibility/shownearbyvehicles API endpoint.
 * 
 * Request Parameters:
 * - shift_id: Optional string (for filtering by shipment/shift)
 * - carrier_id: Optional string (for filtering by carrier/vendor)
 * - latitude: Optional numeric, min: -90, max: 90 (pickup latitude for reference)
 * - longitude: Optional numeric, min: -180, max: 180 (pickup longitude for reference)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns list of nearby vehicles with driver information
 * - Includes vehicle coordinates, driver details, and contact information
 * 
 * @package App\Http\Requests
 */
class ShowNearbyVehiclesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shift_id' => [
                'nullable',
                'string',
                'max:255'
            ],
            'carrier_id' => [
                'nullable',
                'string',
                'max:255'
            ],
            'latitude' => [
                'nullable',
                'numeric',
                'min:-90',
                'max:90'
            ],
            'longitude' => [
                'nullable',
                'numeric',
                'min:-180',
                'max:180'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'shift_id.string' => 'Shift ID must be a valid string.',
            'shift_id.max' => 'Shift ID cannot exceed 255 characters.',
            'carrier_id.string' => 'Carrier ID must be a valid string.',
            'carrier_id.max' => 'Carrier ID cannot exceed 255 characters.',
            'latitude.numeric' => 'Latitude must be a valid number.',
            'latitude.min' => 'Latitude must be between -90 and 90.',
            'latitude.max' => 'Latitude must be between -90 and 90.',
            'longitude.numeric' => 'Longitude must be a valid number.',
            'longitude.min' => 'Longitude must be between -180 and 180.',
            'longitude.max' => 'Longitude must be between -180 and 180.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'shift_id' => 'shift ID',
            'carrier_id' => 'carrier ID',
            'latitude' => 'latitude',
            'longitude' => 'longitude',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed. Please check your input.',
                'data' => null,
                'errors' => $validator->errors()->toArray()
            ], 422)
        );
    }
}

