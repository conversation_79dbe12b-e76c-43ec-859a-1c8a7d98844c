<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Trip Template Store Request Validation
 * 
 * This request class handles validation for the triptemplates store API endpoint.
 * 
 * Request Parameters:
 * - templateName: Required string (template name)
 * - activeTemplate: Optional integer (0 or 1, active status)
 * - templateDescription: Optional string (description)
 * - product: Optional string (product type)
 * - service: Optional string (service type)
 * - orderType: Optional integer (order type)
 * - carrierType: Optional string (carrier type)
 * - shipmentType: Optional string (shipment type)
 * - minimumDistance: Optional numeric (minimum distance)
 * - maximumDistance: Optional numeric (maximum distance)
 * - minimumWeight: Optional numeric (minimum weight)
 * - maximumWeight: Optional numeric (maximum weight)
 * - minimumVolume: Optional numeric (minimum volume)
 * - maximumVolume: Optional numeric (maximum volume)
 * - container_number: Optional string (container number)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Creates a new trip template
 * 
 * @package App\Http\Requests
 */
class TripTemplateStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'templateName' => [
                'required',
                'string',
                'max:255',
                'not_regex:/(example_|your_|test_)/i', // Prevent placeholder values
            ],
            'activeTemplate' => [
                'nullable',
                'integer',
                'in:0,1',
            ],
            'templateDescription' => [
                'nullable',
                'string',
                'max:500',
            ],
            'product' => [
                'nullable',
                'string',
                'max:255',
            ],
            'service' => [
                'nullable',
                'string',
                'max:255',
            ],
            'orderType' => [
                'nullable',
                'integer',
                'min:0',
                'max:999999',
            ],
            'carrierType' => [
                'required',
                'string',
                'max:255',
            ],
            'shipmentType' => [
                'required',
                'string',
                'max:255',
            ],
            'minimumDistance' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'maximumDistance' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'minimumWeight' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'maximumWeight' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'minimumVolume' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'maximumVolume' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'container_number' => [
                'nullable',
                'string',
                'max:100',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'templateName.required' => 'Template name is required.',
            'templateName.string' => 'Template name must be a string.',
            'templateName.max' => 'Template name must not exceed 255 characters.',
            'templateName.not_regex' => 'Template name cannot contain placeholder values like example_, your_, or test_.',
            'activeTemplate.integer' => 'Active template must be an integer.',
            'activeTemplate.in' => 'Active template must be 0 or 1.',
            'templateDescription.max' => 'Description must not exceed 500 characters.',
            'product.max' => 'Product must not exceed 255 characters.',
            'service.max' => 'Service must not exceed 255 characters.',
            'orderType.integer' => 'Order type must be an integer.',
            'orderType.min' => 'Order type must be at least 0.',
            'carrierType.max' => 'Carrier type must not exceed 255 characters.',
            'shipmentType.max' => 'Shipment type must not exceed 255 characters.',
            'minimumDistance.numeric' => 'Minimum distance must be numeric.',
            'minimumDistance.min' => 'Minimum distance must be at least 0.',
            'maximumDistance.numeric' => 'Maximum distance must be numeric.',
            'maximumDistance.min' => 'Maximum distance must be at least 0.',
            'minimumWeight.numeric' => 'Minimum weight must be numeric.',
            'minimumWeight.min' => 'Minimum weight must be at least 0.',
            'maximumWeight.numeric' => 'Maximum weight must be numeric.',
            'maximumWeight.min' => 'Maximum weight must be at least 0.',
            'minimumVolume.numeric' => 'Minimum volume must be numeric.',
            'minimumVolume.min' => 'Minimum volume must be at least 0.',
            'maximumVolume.numeric' => 'Maximum volume must be numeric.',
            'maximumVolume.min' => 'Maximum volume must be at least 0.',
            'container_number.max' => 'Container number must not exceed 100 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'templateName' => 'Template Name',
            'activeTemplate' => 'Active Template',
            'templateDescription' => 'Description',
            'product' => 'Product',
            'service' => 'Service',
            'orderType' => 'Order Type',
            'carrierType' => 'Carrier Type',
            'shipmentType' => 'Shipment Type',
            'minimumDistance' => 'Minimum Distance',
            'maximumDistance' => 'Maximum Distance',
            'minimumWeight' => 'Minimum Weight',
            'maximumWeight' => 'Maximum Weight',
            'minimumVolume' => 'Minimum Volume',
            'maximumVolume' => 'Maximum Volume',
            'container_number' => 'Container Number',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'false',
                'message' => 'Validation failed: ' . implode(', ', $validator->errors()->all()),
                'data' => null,
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}

