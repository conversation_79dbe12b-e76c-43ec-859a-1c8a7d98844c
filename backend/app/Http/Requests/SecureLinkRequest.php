<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Secure Link Request Validation
 * 
 * This request class handles validation for the visibility/securelink API endpoint.
 * 
 * Request Parameters:
 * - ship_id: Required string (shipment identifier)
 * - vendor_id: Required integer (vendor ID)
 * - shipmentid: Required string (shipment ID for JWT generation)
 * - voyage_number: Optional string (voyage number)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * 
 * Response Format:
 * - Returns secure links for driver app and carrier booking
 * - Includes JWT tokens embedded in URLs
 * 
 * @package App\Http\Requests
 */
class SecureLinkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'ship_id' => [
                'required',
                'string',
                'max:255'
            ],
            'vendor_id' => [
                'required',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'shipmentid' => [
                'required',
                'string',
                'max:255'
            ],
            'voyage_number' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'ship_id.required' => 'Ship ID is required.',
            'ship_id.string' => 'Ship ID must be a valid string.',
            'ship_id.max' => 'Ship ID cannot exceed 255 characters.',
            
            'vendor_id.required' => 'Vendor ID is required.',
            'vendor_id.integer' => 'Vendor ID must be a valid integer.',
            'vendor_id.min' => 'Vendor ID must be at least 1.',
            'vendor_id.max' => 'Vendor ID cannot exceed 999,999,999.',
            
            'shipmentid.required' => 'Shipment ID is required.',
            'shipmentid.string' => 'Shipment ID must be a valid string.',
            'shipmentid.max' => 'Shipment ID cannot exceed 255 characters.',
            
            'voyage_number.string' => 'Voyage number must be a valid string.',
            'voyage_number.max' => 'Voyage number cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'ship_id' => 'Ship ID',
            'vendor_id' => 'Vendor ID',
            'shipmentid' => 'Shipment ID',
            'voyage_number' => 'Voyage Number',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
            // For example, business-specific rules
        });
    }
}

