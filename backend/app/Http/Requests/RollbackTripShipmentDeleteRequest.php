<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Rollback Trip Shipment Delete Request Validation
 * 
 * This request class handles validation for the visibility/rollbacktripsandshipmentdelete API endpoint.
 * 
 * Request Parameters:
 * - id: Required integer (shipment/shift ID for rollback and deletion)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns rollback confirmation with shift_id and deleted_at timestamp
 * - Logs activity for each affected order
 * 
 * Business Logic:
 * - Rolls back order assignments (sets shift_id, trip_id, trip_sts to 0)
 * - Updates shipment and order stops status to 0
 * - Updates shift vehicle status to 0
 * - Deletes shipment (sets status to 0)
 * - Prevents deletion if trip is running (status = '1')
 * 
 * @package App\Http\Requests
 */
class RollbackTripShipmentDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'integer',
                'min:1',
                'max:999999999',
                'exists:shipment,id'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'id.required' => 'Shipment ID is required.',
            'id.integer' => 'Shipment ID must be a valid integer.',
            'id.min' => 'Shipment ID must be at least 1.',
            'id.max' => 'Shipment ID cannot exceed 999,999,999.',
            'id.exists' => 'The selected shipment ID does not exist in the system.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'id' => 'Shipment ID',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Merge route parameter into request data for validation
        $this->merge([
            'id' => $this->route('id')
        ]);
    }
}

