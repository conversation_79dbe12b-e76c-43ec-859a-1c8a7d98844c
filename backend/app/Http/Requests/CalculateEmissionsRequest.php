<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Calculate Emissions Request Validation
 * 
 * This request class handles validation for the visibility/calculateemissions API endpoint.
 * 
 * Request Parameters:
 * - weight: Optional numeric (weight in tons for emission calculation)
 * - slat: Required numeric (start latitude)
 * - slng: Required numeric (start longitude)
 * - elat: Required numeric (end latitude)
 * - elng: Required numeric (end longitude)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns CO2 emission calculations in grams
 * - Includes weight, distance in miles, and total emissions
 * 
 * @package App\Http\Requests
 */
class CalculateEmissionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'weight' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999'
            ],
            'slat' => [
                'required',
                'numeric',
                'between:-90,90'
            ],
            'slng' => [
                'required',
                'numeric',
                'between:-180,180'
            ],
            'elat' => [
                'required',
                'numeric',
                'between:-90,90'
            ],
            'elng' => [
                'required',
                'numeric',
                'between:-180,180'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'weight.numeric' => 'Weight must be a valid number.',
            'weight.min' => 'Weight must be at least 0.',
            'weight.max' => 'Weight cannot exceed 999,999.',
            
            'slat.required' => 'Start latitude is required.',
            'slat.numeric' => 'Start latitude must be a valid number.',
            'slat.between' => 'Start latitude must be between -90 and 90.',
            
            'slng.required' => 'Start longitude is required.',
            'slng.numeric' => 'Start longitude must be a valid number.',
            'slng.between' => 'Start longitude must be between -180 and 180.',
            
            'elat.required' => 'End latitude is required.',
            'elat.numeric' => 'End latitude must be a valid number.',
            'elat.between' => 'End latitude must be between -90 and 90.',
            
            'elng.required' => 'End longitude is required.',
            'elng.numeric' => 'End longitude must be a valid number.',
            'elng.between' => 'End longitude must be between -180 and 180.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'weight' => 'Weight',
            'slat' => 'Start Latitude',
            'slng' => 'Start Longitude',
            'elat' => 'End Latitude',
            'elng' => 'End Longitude',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
            // For example, business-specific rules
        });
    }
}

