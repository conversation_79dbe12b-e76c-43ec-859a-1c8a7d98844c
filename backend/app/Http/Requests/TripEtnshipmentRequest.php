<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Trip Etnshipment Request Validation
 * 
 * This request class handles validation for the visibility/tripetnshipment API endpoint.
 * 
 * Request Parameters:
 * - shift_id: Required integer (shipment/shift ID for trip details)
 * - trip_no: Optional string (trip number)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns comprehensive trip and shipment details
 * - Includes vehicle information, driver details, order data, and route information
 * 
 * @package App\Http\Requests
 */
class TripEtnshipmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shift_id' => [
                'required',
                'integer',
                'min:1',
                'max:999999999',
                'exists:shipment,id'
            ],
            'trip_id' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'shift_id.required' => 'Shift ID is required.',
            'shift_id.integer' => 'Shift ID must be a valid integer.',
            'shift_id.min' => 'Shift ID must be at least 1.',
            'shift_id.max' => 'Shift ID cannot exceed 999,999,999.',
            'shift_id.exists' => 'The selected Shift ID does not exist in the system.',
            
            'trip_id.string' => 'Trip ID must be a valid string.',
            'trip_id.max' => 'Trip ID cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'shift_id' => 'Shift ID',
            'trip_id' => 'Trip ID',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
            // For example, checking if shift_id exists in shipment table
        });
    }
}

