<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Trip Template Index Request Validation
 * 
 * This request class handles validation for the triptemplates index API endpoint.
 * 
 * Request Parameters:
 * - org_id: Optional string (organization ID for filtering)
 * - be_value: Optional string (business entity value for filtering)
 * - searchsubmit: Optional string (search submit button value)
 * - template_id: Optional integer (template ID for search)
 * - template_name: Optional string (template name for search)
 * - searchsubmita: Optional string (advanced search submit button value)
 * - active: Optional integer (active status for search)
 * - service: Optional integer (service ID for search)
 * - customer_id: Optional integer (customer ID for search)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns trip templates with related services, customers, and order types
 * 
 * @package App\Http\Requests
 */
class TripTemplateIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'org_id' => [
                'nullable',
                'string',
                'max:255'
            ],
            'be_value' => [
                'nullable',
                'string',
                'max:255'
            ],
            'searchsubmit' => [
                'nullable',
                'string',
                'in:Search'
            ],
            'template_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'template_name' => [
                'nullable',
                'string',
                'max:255'
            ],
            'searchsubmita' => [
                'nullable',
                'string',
                'in:Search'
            ],
            'active' => [
                'nullable',
                'integer',
                'in:0,1'
            ],
            'service' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'customer_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'org_id.string' => 'Organization ID must be a valid string.',
            'org_id.max' => 'Organization ID cannot exceed 255 characters.',
            
            'be_value.string' => 'Business entity value must be a valid string.',
            'be_value.max' => 'Business entity value cannot exceed 255 characters.',
            
            'searchsubmit.in' => 'Search submit value must be "Search".',
            
            'template_id.integer' => 'Template ID must be a valid integer.',
            'template_id.min' => 'Template ID must be at least 1.',
            'template_id.max' => 'Template ID cannot exceed 999,999,999.',
            
            'template_name.string' => 'Template name must be a valid string.',
            'template_name.max' => 'Template name cannot exceed 255 characters.',
            
            'searchsubmita.in' => 'Advanced search submit value must be "Search".',
            
            'active.integer' => 'Active status must be a valid integer.',
            'active.in' => 'Active status must be either 0 or 1.',
            
            'service.integer' => 'Service ID must be a valid integer.',
            'service.min' => 'Service ID must be at least 1.',
            'service.max' => 'Service ID cannot exceed 999,999,999.',
            
            'customer_id.integer' => 'Customer ID must be a valid integer.',
            'customer_id.min' => 'Customer ID must be at least 1.',
            'customer_id.max' => 'Customer ID cannot exceed 999,999,999.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'org_id' => 'Organization ID',
            'be_value' => 'Business Entity Value',
            'searchsubmit' => 'Search Submit',
            'template_id' => 'Template ID',
            'template_name' => 'Template Name',
            'searchsubmita' => 'Advanced Search Submit',
            'active' => 'Active Status',
            'service' => 'Service',
            'customer_id' => 'Customer ID',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
        });
    }
}

