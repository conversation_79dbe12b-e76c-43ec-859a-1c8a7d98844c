<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Assign Vehicle Request Validation
 * 
 * This request class handles validation for the visibility/assignvehicle API endpoint.
 * 
 * Request Parameters:
 * - shift_id: Required integer, min: 1 (shipment/shift ID)
 * - carrier_id: Required integer, min: 1 (carrier/vendor ID)
 * - volume: Optional numeric, min: 0 (cargo volume)
 * - vehicle_id: Optional integer, min: 1 (vehicle ID)
 * 
 * Authentication:
 * - Requires valid JWT token via Auth::guard('api')
 * - Validates user ID and organization ID
 * 
 * Response Format:
 * - Returns shipment details for vehicle assignment
 * - Includes driver, vehicle, carrier, and rate information
 * 
 * @package App\Http\Requests
 */
class AssignVehicleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shift_id' => [
                'required',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'carrier_id' => [
                'required',
                'integer',
                'min:1',
                'max:999999999'
            ],
            'volume' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99'
            ],
            'vehicle_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:999999999'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'shift_id.required' => 'Shift ID is required.',
            'shift_id.integer' => 'Shift ID must be a valid integer.',
            'shift_id.min' => 'Shift ID must be at least 1.',
            'shift_id.max' => 'Shift ID cannot exceed 999,999,999.',
            
            'carrier_id.required' => 'Carrier ID is required.',
            'carrier_id.integer' => 'Carrier ID must be a valid integer.',
            'carrier_id.min' => 'Carrier ID must be at least 1.',
            'carrier_id.max' => 'Carrier ID cannot exceed 999,999,999.',
            
            'volume.numeric' => 'Volume must be a valid number.',
            'volume.min' => 'Volume must be 0 or greater.',
            'volume.max' => 'Volume cannot exceed 999,999.99.',
            
            'vehicle_id.integer' => 'Vehicle ID must be a valid integer.',
            'vehicle_id.min' => 'Vehicle ID must be at least 1.',
            'vehicle_id.max' => 'Vehicle ID cannot exceed 999,999,999.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'shift_id' => 'Shift ID',
            'carrier_id' => 'Carrier ID',
            'volume' => 'Volume',
            'vehicle_id' => 'Vehicle ID',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed: ' . $validator->errors()->first(),
                'data' => null,
                'errors' => $validator->errors()->all(),
            ], 422)
        );
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here if needed
            // For example, checking if shift_id exists in shipment table
            // or validating carrier_id exists in sx_users table
        });
    }
}
